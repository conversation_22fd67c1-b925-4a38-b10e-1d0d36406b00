"""
Captcha API Configuration

Store your API keys and credentials for various captcha solving services here.
Copy this file to captcha_api_config_local.py and add your actual keys.
"""

# 2Captcha.com Configuration
# Sign up at: https://2captcha.com/
# Cost: $1-3 per 1000 captchas
# Accuracy: 95-99%
TWOCAPTCHA_API_KEY = "your_2captcha_api_key_here"

# Anti-Captcha.com Configuration  
# Sign up at: https://anti-captcha.com/
# Cost: $1.5-3 per 1000 captchas
# Accuracy: 95-99%
ANTICAPTCHA_API_KEY = "your_anticaptcha_api_key_here"

# DeathByCaptcha Configuration
# Sign up at: https://www.deathbycaptcha.com/
# Cost: $1.39 per 1000 captchas
# Accuracy: 90-95%
DEATHBYCAPTCHA_USERNAME = "your_dbc_username"
DEATHBYCAPTCHA_PASSWORD = "your_dbc_password"

# CapMonster.cloud Configuration
# Sign up at: https://capmonster.cloud/
# Cost: $0.8-2 per 1000 captchas
# Accuracy: 95-98%
CAPMONSTER_API_KEY = "your_capmonster_api_key_here"

# Service Priority (1 = highest priority)
# Adjust based on your preferences and account balances
SERVICE_PRIORITY = {
    'audio_captcha': 1,      # Try audio first (free)
    '2captcha': 2,           # High accuracy, good speed
    'anticaptcha': 3,        # High accuracy, fast
    'deathbycaptcha': 4,     # Lower cost option
    'capmonster': 5          # Alternative service
}

# Budget Controls
MAX_DAILY_SPEND = 10.0      # Maximum daily spend in USD
MAX_MONTHLY_SPEND = 100.0   # Maximum monthly spend in USD
COST_PER_CAPTCHA = {
    '2captcha': 0.002,
    'anticaptcha': 0.002,
    'deathbycaptcha': 0.00139,
    'capmonster': 0.0015
}

# Service Settings
TIMEOUT_SECONDS = 300       # Maximum time to wait for solution
RETRY_FAILED_SERVICES = True # Retry with other services if one fails
SAVE_SOLVED_CAPTCHAS = True  # Save successfully solved captchas for analysis

# Audio Captcha Settings
AUDIO_CAPTCHA_ENABLED = True
SPEECH_RECOGNITION_SERVICES = [
    'google',    # Google Speech Recognition (free, requires internet)
    'sphinx'     # CMU Sphinx (offline, less accurate)
]

# Debug Settings
DEBUG_MODE = True
SAVE_DEBUG_IMAGES = True
LOG_API_RESPONSES = False    # Set to True for debugging API issues

def get_api_keys():
    """
    Get API keys dictionary for the captcha solver.

    Returns:
        dict: API keys and credentials
    """
    try:
        # Try to import local config first
        import captcha_api_config_local as local_config
        return {
            '2captcha': local_config.TWOCAPTCHA_API_KEY,
            'anticaptcha': local_config.ANTICAPTCHA_API_KEY,
            'dbc_username': local_config.DEATHBYCAPTCHA_USERNAME,
            'dbc_password': local_config.DEATHBYCAPTCHA_PASSWORD,
            'capmonster': local_config.CAPMONSTER_API_KEY
        }
    except ImportError:
        # Fall back to this file's values
        return {
            '2captcha': TWOCAPTCHA_API_KEY,
            'anticaptcha': ANTICAPTCHA_API_KEY,
            'dbc_username': DEATHBYCAPTCHA_USERNAME,
            'dbc_password': DEATHBYCAPTCHA_PASSWORD,
            'capmonster': CAPMONSTER_API_KEY
        }

def validate_api_keys():
    """
    Validate that at least one API key is configured.
    
    Returns:
        tuple: (is_valid, message)
    """
    api_keys = get_api_keys()
    
    valid_keys = []
    if api_keys.get('2captcha') and api_keys['2captcha'] != 'your_2captcha_api_key_here':
        valid_keys.append('2captcha')
    
    if api_keys.get('anticaptcha') and api_keys['anticaptcha'] != 'your_anticaptcha_api_key_here':
        valid_keys.append('anticaptcha')
    
    if (api_keys.get('dbc_username') and api_keys['dbc_username'] != 'your_dbc_username' and
        api_keys.get('dbc_password') and api_keys['dbc_password'] != 'your_dbc_password'):
        valid_keys.append('deathbycaptcha')
    
    if api_keys.get('capmonster') and api_keys['capmonster'] != 'your_capmonster_api_key_here':
        valid_keys.append('capmonster')
    
    if valid_keys:
        return True, f"Valid API keys found for: {', '.join(valid_keys)}"
    else:
        return False, "No valid API keys configured. Please update captcha_api_config_local.py"

# Usage Instructions
SETUP_INSTRUCTIONS = """
🚀 SETUP INSTRUCTIONS:

1. Copy this file to 'captcha_api_config_local.py'
2. Sign up for at least one captcha solving service:
   
   📧 2Captcha.com (Recommended)
   - Visit: https://2captcha.com/
   - Sign up and get API key
   - Add funds to account ($5-10 minimum)
   
   📧 Anti-Captcha.com (Alternative)
   - Visit: https://anti-captcha.com/
   - Sign up and get API key
   - Add funds to account
   
   📧 DeathByCaptcha (Budget Option)
   - Visit: https://www.deathbycaptcha.com/
   - Create account with username/password
   - Add funds to account

3. Update captcha_api_config_local.py with your actual keys
4. Test the setup with: python test_captcha_services.py

💡 TIPS:
- Start with 2Captcha - it has the best accuracy
- Keep multiple services as backup
- Monitor your spending with the budget controls
- Audio captcha is free but may not always be available
"""

if __name__ == "__main__":
    print(SETUP_INSTRUCTIONS)
    is_valid, message = validate_api_keys()
    print(f"\n🔍 Validation: {message}")
