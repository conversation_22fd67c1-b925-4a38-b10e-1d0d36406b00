#!/usr/bin/env python3
"""
Test script to verify enhanced result extraction functionality
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stealth_aadhaar_checker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_parsing():
    """Test the parsing functionality with sample data"""
    
    # Create checker instance
    checker = StealthAadhaarChecker(debug=True)
    
    # Test data that mimics actual UIDAI response
    test_data = """
    ************ Exists
    Aadhaar Verification Completed
    Age Band
    20-30 years
    Gender
    MALE
    State
    Maharashtra
    Mobile
    *******671
    """
    
    print("🧪 Testing result parsing with sample data...")
    print(f"📄 Sample data: {test_data}")
    
    # Test parsing
    result = checker._parse_validation_details(test_data)
    
    print("\n📊 Parsed Results:")
    for key, value in result.items():
        print(f"   {key}: {value}")
    
    # Test with compact format
    compact_data = "************ Exists\nAadhaar Verification Completed\nAge Band: 20-30 years\nGender: MALE\nState: Maharashtra\nMobile: *******671"
    
    print("\n🧪 Testing with compact format...")
    print(f"📄 Compact data: {compact_data}")
    
    result2 = checker._parse_validation_details(compact_data)
    
    print("\n📊 Compact Parsed Results:")
    for key, value in result2.items():
        print(f"   {key}: {value}")
    
    checker.close()

if __name__ == "__main__":
    test_parsing()
