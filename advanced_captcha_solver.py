#!/usr/bin/env python3
"""
Advanced Non-OCR Captcha Solver

This script provides various non-OCR techniques for solving captchas,
including paid services, audio captcha, AI models, and stealth techniques.
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import pytesseract
import base64
from io import BytesIO
import os
import time
import requests
import json
from datetime import datetime
import speech_recognition as sr
import random


class NonOCRCaptchaSolver:
    """
    Advanced non-OCR captcha solver with multiple solving techniques.
    """

    def __init__(self, debug=True, api_keys=None):
        self.debug = debug
        self.debug_dir = "captcha_debug"
        if self.debug and not os.path.exists(self.debug_dir):
            os.makedirs(self.debug_dir)

        # API keys for paid services
        self.api_keys = api_keys or {}

        # Initialize speech recognition for audio captcha
        self.speech_recognizer = sr.Recognizer()

        # Success rates tracking
        self.method_stats = {
            'manual_input': {'attempts': 0, 'success': 0},
            'audio_captcha': {'attempts': 0, 'success': 0},
            'twocaptcha': {'attempts': 0, 'success': 0},
            'anticaptcha': {'attempts': 0, 'success': 0},
            'deathbycaptcha': {'attempts': 0, 'success': 0}
        }
    
    def solve_captcha_from_base64(self, base64_string, driver=None):
        """
        Solve captcha from base64 string using non-OCR methods.

        Args:
            base64_string (str): Base64 encoded image
            driver: Selenium WebDriver instance (for audio captcha)

        Returns:
            dict: Results from different solving methods
        """
        try:
            # Decode base64 to image
            if base64_string.startswith("data:"):
                base64_string = base64_string.split(",", 1)[1]

            image_data = base64.b64decode(base64_string)
            image = Image.open(BytesIO(image_data))

            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Try different non-OCR methods in order of preference
            result = self._solve_with_hybrid_approach(base64_string, image, driver)

            return result

        except Exception as e:
            return {
                'error': f'Failed to solve captcha: {str(e)}',
                'success': False,
                'method': 'error'
            }

    def _solve_with_hybrid_approach(self, base64_string, image, driver):
        """
        Try multiple non-OCR methods in order of preference.
        """
        methods_to_try = [
            ('manual_input', self._try_manual_input),
            ('audio_captcha', self._try_audio_captcha),
            ('twocaptcha', self._solve_with_2captcha),
            ('anticaptcha', self._solve_with_anticaptcha),
            ('deathbycaptcha', self._solve_with_deathbycaptcha)
        ]

        for method_name, method_func in methods_to_try:
            try:
                if self.debug:
                    print(f"🔄 Trying {method_name}...")

                if method_name == 'manual_input':
                    result = method_func(base64_string, image)
                elif method_name == 'audio_captcha' and driver:
                    result = method_func(driver)
                else:
                    result = method_func(base64_string)

                if result and result.get('success'):
                    self._update_stats(method_name, True)
                    if self.debug:
                        print(f"✅ {method_name} succeeded: {result.get('text', 'N/A')}")
                    return result
                else:
                    self._update_stats(method_name, False)
                    if self.debug:
                        print(f"❌ {method_name} failed")

            except Exception as e:
                self._update_stats(method_name, False)
                if self.debug:
                    print(f"❌ {method_name} error: {str(e)}")
                continue

        return {
            'error': 'All captcha solving methods failed',
            'success': False,
            'method': 'all_failed'
        }

    def _try_manual_input(self, base64_string, image):
        """
        Manual captcha input mode - displays the captcha and asks user to enter the text.
        """
        try:
            import tkinter as tk
            from tkinter import messagebox, simpledialog
            import tempfile
            import os
            from PIL import ImageTk

            # Save the captcha image to a temporary file for display
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
            image.save(temp_file.name)
            temp_file.close()

            # Create a simple GUI to display the captcha and get user input
            root = tk.Tk()
            root.title("Manual Captcha Input")
            root.geometry("600x400")
            root.configure(bg='white')

            # Center the window
            root.eval('tk::PlaceWindow . center')

            # Load and display the captcha image
            try:
                # Resize image for better visibility
                display_image = image.copy()
                # Scale up the image for better visibility
                width, height = display_image.size
                scale_factor = min(400 / width, 200 / height, 3)  # Max 3x scale
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)
                display_image = display_image.resize((new_width, new_height), Image.Resampling.NEAREST)

                photo = ImageTk.PhotoImage(display_image)

                # Create and pack widgets
                title_label = tk.Label(root, text="🧩 Manual Captcha Solver",
                                     font=("Arial", 16, "bold"), bg='white', fg='#2c3e50')
                title_label.pack(pady=10)

                instruction_label = tk.Label(root, text="Please enter the text you see in the captcha image below:",
                                           font=("Arial", 11), bg='white', fg='#34495e')
                instruction_label.pack(pady=5)

                # Image frame with border
                image_frame = tk.Frame(root, bg='#ecf0f1', relief='solid', bd=2)
                image_frame.pack(pady=10)

                image_label = tk.Label(image_frame, image=photo, bg='#ecf0f1')
                image_label.pack(padx=10, pady=10)

                # Input frame
                input_frame = tk.Frame(root, bg='white')
                input_frame.pack(pady=10)

                input_label = tk.Label(input_frame, text="Captcha Text:",
                                     font=("Arial", 12, "bold"), bg='white', fg='#2c3e50')
                input_label.pack()

                # Entry widget for captcha text
                captcha_var = tk.StringVar()
                entry = tk.Entry(input_frame, textvariable=captcha_var, font=("Arial", 14),
                               width=20, justify='center', relief='solid', bd=2)
                entry.pack(pady=5)
                entry.focus()

                # Result variable
                result_text = None

                def submit_captcha():
                    nonlocal result_text
                    text = captcha_var.get().strip()
                    if text:
                        result_text = text
                        root.quit()
                    else:
                        messagebox.showwarning("Warning", "Please enter the captcha text!")

                def skip_captcha():
                    nonlocal result_text
                    result_text = None
                    root.quit()

                # Button frame
                button_frame = tk.Frame(root, bg='white')
                button_frame.pack(pady=15)

                submit_btn = tk.Button(button_frame, text="✅ Submit", command=submit_captcha,
                                     font=("Arial", 12, "bold"), bg='#27ae60', fg='white',
                                     padx=20, pady=5, relief='flat', cursor='hand2')
                submit_btn.pack(side=tk.LEFT, padx=10)

                skip_btn = tk.Button(button_frame, text="⏭️ Skip", command=skip_captcha,
                                   font=("Arial", 12), bg='#95a5a6', fg='white',
                                   padx=20, pady=5, relief='flat', cursor='hand2')
                skip_btn.pack(side=tk.LEFT, padx=10)

                # Bind Enter key to submit
                entry.bind('<Return>', lambda e: submit_captcha())

                # Instructions at bottom
                help_label = tk.Label(root, text="💡 Tip: Enter the text exactly as shown, then press Enter or click Submit",
                                    font=("Arial", 9), bg='white', fg='#7f8c8d')
                help_label.pack(pady=5)

                # Keep reference to photo to prevent garbage collection
                root.photo = photo

                # Start the GUI
                root.mainloop()
                root.destroy()

                # Clean up temp file
                try:
                    os.unlink(temp_file.name)
                except:
                    pass

                if result_text:
                    return {
                        'success': True,
                        'text': result_text,
                        'method': 'manual_input',
                        'confidence': 1.0
                    }
                else:
                    return {
                        'success': False,
                        'error': 'User skipped manual input',
                        'method': 'manual_input'
                    }

            except Exception as gui_error:
                # Fallback to console input if GUI fails
                print(f"GUI failed ({gui_error}), falling back to console input...")
                return self._console_manual_input(temp_file.name)

        except Exception as e:
            return {
                'success': False,
                'error': f'Manual input failed: {str(e)}',
                'method': 'manual_input'
            }

    def _console_manual_input(self, image_path):
        """
        Fallback console-based manual input.
        """
        try:
            print("\n" + "="*60)
            print("🧩 MANUAL CAPTCHA INPUT MODE")
            print("="*60)
            print(f"📸 Captcha image saved to: {image_path}")
            print("📋 Please open the image file and view the captcha.")
            print("⌨️  Then enter the captcha text below.")
            print("-"*60)

            # Try to open the image automatically
            try:
                import subprocess
                import platform

                if platform.system() == 'Windows':
                    os.startfile(image_path)
                elif platform.system() == 'Darwin':  # macOS
                    subprocess.run(['open', image_path])
                else:  # Linux
                    subprocess.run(['xdg-open', image_path])
                print("✅ Image opened automatically")
            except:
                print("⚠️  Please manually open the image file to view the captcha")

            print("-"*60)

            # Get user input
            while True:
                try:
                    captcha_text = input("🔤 Enter captcha text (or 'skip' to skip): ").strip()

                    if captcha_text.lower() == 'skip':
                        return {
                            'success': False,
                            'error': 'User skipped manual input',
                            'method': 'manual_input'
                        }
                    elif captcha_text:
                        print(f"✅ Captcha text entered: '{captcha_text}'")
                        return {
                            'success': True,
                            'text': captcha_text,
                            'method': 'manual_input',
                            'confidence': 1.0
                        }
                    else:
                        print("❌ Please enter some text or 'skip'")

                except KeyboardInterrupt:
                    print("\n❌ Manual input cancelled")
                    return {
                        'success': False,
                        'error': 'Manual input cancelled',
                        'method': 'manual_input'
                    }

        except Exception as e:
            return {
                'success': False,
                'error': f'Console manual input failed: {str(e)}',
                'method': 'manual_input'
            }
        finally:
            # Clean up temp file
            try:
                os.unlink(image_path)
            except:
                pass

    def _try_audio_captcha(self, driver):
        """
        Try to solve audio captcha if available.
        """
        try:
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            # Look for audio captcha button
            audio_selectors = [
                "button[title*='audio']",
                "button[aria-label*='audio']",
                ".audio-captcha",
                "[onclick*='audio']",
                "img[alt*='audio']",
                "button[title*='Audio']",
                "a[href*='audio']",
                ".captcha-audio"
            ]

            audio_button = None
            for selector in audio_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            audio_button = element
                            break
                    if audio_button:
                        break
                except:
                    continue

            if not audio_button:
                return {'success': False, 'error': 'No audio captcha button found'}

            # Click audio button
            driver.execute_script("arguments[0].click();", audio_button)
            time.sleep(2)

            # Look for audio element or download link
            audio_selectors = [
                "audio",
                "source[src*='.wav']",
                "source[src*='.mp3']",
                "a[href*='.wav']",
                "a[href*='.mp3']",
                "[src*='audio']"
            ]

            audio_url = None
            for selector in audio_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        src = element.get_attribute('src') or element.get_attribute('href')
                        if src and ('wav' in src.lower() or 'mp3' in src.lower() or 'audio' in src.lower()):
                            audio_url = src
                            break
                    if audio_url:
                        break
                except:
                    continue

            if not audio_url:
                return {'success': False, 'error': 'No audio URL found'}

            # Download and process audio
            return self._process_audio_captcha(audio_url)

        except Exception as e:
            return {'success': False, 'error': f'Audio captcha failed: {str(e)}'}

    def _process_audio_captcha(self, audio_url):
        """
        Download and process audio captcha.
        """
        try:
            # Download audio file
            response = requests.get(audio_url, timeout=30)
            if response.status_code != 200:
                return {'success': False, 'error': 'Failed to download audio'}

            # Save temporarily
            temp_audio_path = f"temp_audio_{int(time.time())}.wav"
            with open(temp_audio_path, 'wb') as f:
                f.write(response.content)

            try:
                # Process with speech recognition
                with sr.AudioFile(temp_audio_path) as source:
                    audio_data = self.speech_recognizer.record(source)

                # Try multiple speech recognition services
                text_results = []

                # Google Speech Recognition (free)
                try:
                    text = self.speech_recognizer.recognize_google(audio_data)
                    text_results.append(text.replace(' ', '').upper())
                except:
                    pass

                # Sphinx (offline)
                try:
                    text = self.speech_recognizer.recognize_sphinx(audio_data)
                    text_results.append(text.replace(' ', '').upper())
                except:
                    pass

                # Return best result
                if text_results:
                    # Use the shortest result (usually more accurate for captchas)
                    best_result = min(text_results, key=len)
                    return {
                        'success': True,
                        'text': best_result,
                        'method': 'audio_captcha',
                        'confidence': 0.8,
                        'all_results': text_results
                    }
                else:
                    return {'success': False, 'error': 'Speech recognition failed'}

            finally:
                # Clean up temp file
                if os.path.exists(temp_audio_path):
                    os.remove(temp_audio_path)

        except Exception as e:
            return {'success': False, 'error': f'Audio processing failed: {str(e)}'}

    def _solve_with_2captcha(self, base64_string):
        """
        Solve captcha using 2captcha.com service.
        """
        api_key = self.api_keys.get('2captcha')
        if not api_key:
            return {'success': False, 'error': '2captcha API key not provided'}

        try:
            # Submit captcha
            submit_url = "http://2captcha.com/in.php"
            data = {
                'key': api_key,
                'method': 'base64',
                'body': base64_string,
                'json': 1
            }

            response = requests.post(submit_url, data=data, timeout=30)
            result = response.json()

            if result.get('status') != 1:
                return {'success': False, 'error': f'2captcha submit failed: {result.get("error_text", "Unknown error")}'}

            captcha_id = result.get('request')

            # Wait for solution
            result_url = "http://2captcha.com/res.php"
            max_attempts = 30  # 5 minutes max

            for attempt in range(max_attempts):
                time.sleep(10)  # Wait 10 seconds between checks

                params = {
                    'key': api_key,
                    'action': 'get',
                    'id': captcha_id,
                    'json': 1
                }

                response = requests.get(result_url, params=params, timeout=30)
                result = response.json()

                if result.get('status') == 1:
                    return {
                        'success': True,
                        'text': result.get('request', ''),
                        'method': '2captcha',
                        'confidence': 0.95,
                        'cost': 0.002  # Approximate cost in USD
                    }
                elif result.get('error_text') and result.get('error_text') != 'CAPCHA_NOT_READY':
                    return {'success': False, 'error': f'2captcha error: {result.get("error_text")}'}

            return {'success': False, 'error': '2captcha timeout'}

        except Exception as e:
            return {'success': False, 'error': f'2captcha exception: {str(e)}'}

    def _solve_with_anticaptcha(self, base64_string):
        """
        Solve captcha using anti-captcha.com service.
        """
        api_key = self.api_keys.get('anticaptcha')
        if not api_key:
            return {'success': False, 'error': 'Anti-captcha API key not provided'}

        try:
            # Create task
            create_task_url = "https://api.anti-captcha.com/createTask"
            task_data = {
                "clientKey": api_key,
                "task": {
                    "type": "ImageToTextTask",
                    "body": base64_string
                }
            }

            response = requests.post(create_task_url, json=task_data, timeout=30)
            result = response.json()

            if result.get('errorId') != 0:
                return {'success': False, 'error': f'Anti-captcha create task failed: {result.get("errorDescription")}'}

            task_id = result.get('taskId')

            # Wait for solution
            get_result_url = "https://api.anti-captcha.com/getTaskResult"
            max_attempts = 30

            for attempt in range(max_attempts):
                time.sleep(10)

                result_data = {
                    "clientKey": api_key,
                    "taskId": task_id
                }

                response = requests.post(get_result_url, json=result_data, timeout=30)
                result = response.json()

                if result.get('status') == 'ready':
                    solution = result.get('solution', {})
                    return {
                        'success': True,
                        'text': solution.get('text', ''),
                        'method': 'anticaptcha',
                        'confidence': 0.96,
                        'cost': 0.002
                    }
                elif result.get('errorId') != 0:
                    return {'success': False, 'error': f'Anti-captcha error: {result.get("errorDescription")}'}

            return {'success': False, 'error': 'Anti-captcha timeout'}

        except Exception as e:
            return {'success': False, 'error': f'Anti-captcha exception: {str(e)}'}

    def _solve_with_deathbycaptcha(self, base64_string):
        """
        Solve captcha using deathbycaptcha.com service.
        """
        username = self.api_keys.get('dbc_username')
        password = self.api_keys.get('dbc_password')

        if not username or not password:
            return {'success': False, 'error': 'DeathByCaptcha credentials not provided'}

        try:
            # Submit captcha
            submit_url = "http://api.dbcapi.me/api/captcha"

            # Convert base64 to file-like object
            image_data = base64.b64decode(base64_string)

            files = {'captchafile': ('captcha.png', BytesIO(image_data), 'image/png')}
            data = {
                'username': username,
                'password': password
            }

            response = requests.post(submit_url, files=files, data=data, timeout=30)
            result = response.json()

            if not result.get('captcha'):
                return {'success': False, 'error': f'DeathByCaptcha submit failed: {result.get("error", "Unknown error")}'}

            captcha_id = result.get('captcha')

            # Wait for solution
            max_attempts = 30

            for attempt in range(max_attempts):
                time.sleep(10)

                status_url = f"http://api.dbcapi.me/api/captcha/{captcha_id}"
                response = requests.get(status_url, timeout=30)
                result = response.json()

                if result.get('text'):
                    return {
                        'success': True,
                        'text': result.get('text', ''),
                        'method': 'deathbycaptcha',
                        'confidence': 0.93,
                        'cost': 0.00139
                    }
                elif result.get('is_correct') is False:
                    return {'success': False, 'error': 'DeathByCaptcha failed to solve'}

            return {'success': False, 'error': 'DeathByCaptcha timeout'}

        except Exception as e:
            return {'success': False, 'error': f'DeathByCaptcha exception: {str(e)}'}

    def _update_stats(self, method, success):
        """Update success statistics for methods."""
        if method in self.method_stats:
            self.method_stats[method]['attempts'] += 1
            if success:
                self.method_stats[method]['success'] += 1

    def get_stats(self):
        """Get success statistics for all methods."""
        stats = {}
        for method, data in self.method_stats.items():
            attempts = data['attempts']
            success = data['success']
            success_rate = (success / attempts * 100) if attempts > 0 else 0
            stats[method] = {
                'attempts': attempts,
                'success': success,
                'success_rate': f"{success_rate:.1f}%"
            }
        return stats

    def set_api_keys(self, api_keys):
        """Set API keys for paid services."""
        self.api_keys.update(api_keys)

    def test_services(self):
        """Test all available captcha solving services."""
        test_results = {}

        # Test 2captcha
        if self.api_keys.get('2captcha'):
            try:
                balance_url = "http://2captcha.com/res.php"
                params = {'key': self.api_keys['2captcha'], 'action': 'getbalance'}
                response = requests.get(balance_url, params=params, timeout=10)
                if response.text.startswith('ERROR'):
                    test_results['2captcha'] = {'status': 'error', 'message': response.text}
                else:
                    test_results['2captcha'] = {'status': 'ok', 'balance': f"${response.text}"}
            except Exception as e:
                test_results['2captcha'] = {'status': 'error', 'message': str(e)}
        else:
            test_results['2captcha'] = {'status': 'no_key', 'message': 'API key not provided'}

        # Test Anti-captcha
        if self.api_keys.get('anticaptcha'):
            try:
                balance_url = "https://api.anti-captcha.com/getBalance"
                data = {"clientKey": self.api_keys['anticaptcha']}
                response = requests.post(balance_url, json=data, timeout=10)
                result = response.json()
                if result.get('errorId') == 0:
                    test_results['anticaptcha'] = {'status': 'ok', 'balance': f"${result.get('balance', 0)}"}
                else:
                    test_results['anticaptcha'] = {'status': 'error', 'message': result.get('errorDescription')}
            except Exception as e:
                test_results['anticaptcha'] = {'status': 'error', 'message': str(e)}
        else:
            test_results['anticaptcha'] = {'status': 'no_key', 'message': 'API key not provided'}

        # Test DeathByCaptcha
        if self.api_keys.get('dbc_username') and self.api_keys.get('dbc_password'):
            try:
                balance_url = "http://api.dbcapi.me/api/user"
                data = {
                    'username': self.api_keys['dbc_username'],
                    'password': self.api_keys['dbc_password']
                }
                response = requests.post(balance_url, data=data, timeout=10)
                result = response.json()
                if 'balance' in result:
                    test_results['deathbycaptcha'] = {'status': 'ok', 'balance': f"${result.get('balance', 0)}"}
                else:
                    test_results['deathbycaptcha'] = {'status': 'error', 'message': 'Authentication failed'}
            except Exception as e:
                test_results['deathbycaptcha'] = {'status': 'error', 'message': str(e)}
        else:
            test_results['deathbycaptcha'] = {'status': 'no_key', 'message': 'Credentials not provided'}

        return test_results


# Legacy compatibility - keep the old class name as alias
AdvancedCaptchaSolver = NonOCRCaptchaSolver





