#!/usr/bin/env python3
"""
Advanced Non-OCR Captcha Solver

This script provides various non-OCR techniques for solving captchas,
including paid services, audio captcha, AI models, and stealth techniques.
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import pytesseract
import base64
from io import BytesIO
import os
import time
import requests
import json
from datetime import datetime
import speech_recognition as sr
import random


class NonOCRCaptchaSolver:
    """
    Advanced non-OCR captcha solver with multiple solving techniques.
    """

    def __init__(self, debug=True, api_keys=None):
        self.debug = debug
        self.debug_dir = "captcha_debug"
        if self.debug and not os.path.exists(self.debug_dir):
            os.makedirs(self.debug_dir)

        # API keys for paid services
        self.api_keys = api_keys or {}

        # Initialize speech recognition for audio captcha
        self.speech_recognizer = sr.Recognizer()

        # Success rates tracking
        self.method_stats = {
            'audio_captcha': {'attempts': 0, 'success': 0},
            'twocaptcha': {'attempts': 0, 'success': 0},
            'anticaptcha': {'attempts': 0, 'success': 0},
            'deathbycaptcha': {'attempts': 0, 'success': 0}
        }
    
    def solve_captcha_from_base64(self, base64_string, driver=None):
        """
        Solve captcha from base64 string using non-OCR methods.

        Args:
            base64_string (str): Base64 encoded image
            driver: Selenium WebDriver instance (for audio captcha)

        Returns:
            dict: Results from different solving methods
        """
        try:
            # Decode base64 to image
            if base64_string.startswith("data:"):
                base64_string = base64_string.split(",", 1)[1]

            image_data = base64.b64decode(base64_string)
            image = Image.open(BytesIO(image_data))

            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Try different non-OCR methods in order of preference
            result = self._solve_with_hybrid_approach(base64_string, image, driver)

            return result

        except Exception as e:
            return {
                'error': f'Failed to solve captcha: {str(e)}',
                'success': False,
                'method': 'error'
            }

    def _solve_with_hybrid_approach(self, base64_string, image, driver):
        """
        Try multiple non-OCR methods in order of preference.
        """
        methods_to_try = [
            ('audio_captcha', self._try_audio_captcha),
            ('twocaptcha', self._solve_with_2captcha),
            ('anticaptcha', self._solve_with_anticaptcha),
            ('deathbycaptcha', self._solve_with_deathbycaptcha)
        ]

        for method_name, method_func in methods_to_try:
            try:
                if self.debug:
                    print(f"🔄 Trying {method_name}...")

                if method_name == 'audio_captcha' and driver:
                    result = method_func(driver)
                else:
                    result = method_func(base64_string)

                if result and result.get('success'):
                    self._update_stats(method_name, True)
                    if self.debug:
                        print(f"✅ {method_name} succeeded: {result.get('text', 'N/A')}")
                    return result
                else:
                    self._update_stats(method_name, False)
                    if self.debug:
                        print(f"❌ {method_name} failed")

            except Exception as e:
                self._update_stats(method_name, False)
                if self.debug:
                    print(f"❌ {method_name} error: {str(e)}")
                continue

        return {
            'error': 'All captcha solving methods failed',
            'success': False,
            'method': 'all_failed'
        }

    def _try_audio_captcha(self, driver):
        """
        Try to solve audio captcha if available.
        """
        try:
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            # Look for audio captcha button
            audio_selectors = [
                "button[title*='audio']",
                "button[aria-label*='audio']",
                ".audio-captcha",
                "[onclick*='audio']",
                "img[alt*='audio']",
                "button[title*='Audio']",
                "a[href*='audio']",
                ".captcha-audio"
            ]

            audio_button = None
            for selector in audio_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            audio_button = element
                            break
                    if audio_button:
                        break
                except:
                    continue

            if not audio_button:
                return {'success': False, 'error': 'No audio captcha button found'}

            # Click audio button
            driver.execute_script("arguments[0].click();", audio_button)
            time.sleep(2)

            # Look for audio element or download link
            audio_selectors = [
                "audio",
                "source[src*='.wav']",
                "source[src*='.mp3']",
                "a[href*='.wav']",
                "a[href*='.mp3']",
                "[src*='audio']"
            ]

            audio_url = None
            for selector in audio_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        src = element.get_attribute('src') or element.get_attribute('href')
                        if src and ('wav' in src.lower() or 'mp3' in src.lower() or 'audio' in src.lower()):
                            audio_url = src
                            break
                    if audio_url:
                        break
                except:
                    continue

            if not audio_url:
                return {'success': False, 'error': 'No audio URL found'}

            # Download and process audio
            return self._process_audio_captcha(audio_url)

        except Exception as e:
            return {'success': False, 'error': f'Audio captcha failed: {str(e)}'}

    def _process_audio_captcha(self, audio_url):
        """
        Download and process audio captcha.
        """
        try:
            # Download audio file
            response = requests.get(audio_url, timeout=30)
            if response.status_code != 200:
                return {'success': False, 'error': 'Failed to download audio'}

            # Save temporarily
            temp_audio_path = f"temp_audio_{int(time.time())}.wav"
            with open(temp_audio_path, 'wb') as f:
                f.write(response.content)

            try:
                # Process with speech recognition
                with sr.AudioFile(temp_audio_path) as source:
                    audio_data = self.speech_recognizer.record(source)

                # Try multiple speech recognition services
                text_results = []

                # Google Speech Recognition (free)
                try:
                    text = self.speech_recognizer.recognize_google(audio_data)
                    text_results.append(text.replace(' ', '').upper())
                except:
                    pass

                # Sphinx (offline)
                try:
                    text = self.speech_recognizer.recognize_sphinx(audio_data)
                    text_results.append(text.replace(' ', '').upper())
                except:
                    pass

                # Return best result
                if text_results:
                    # Use the shortest result (usually more accurate for captchas)
                    best_result = min(text_results, key=len)
                    return {
                        'success': True,
                        'text': best_result,
                        'method': 'audio_captcha',
                        'confidence': 0.8,
                        'all_results': text_results
                    }
                else:
                    return {'success': False, 'error': 'Speech recognition failed'}

            finally:
                # Clean up temp file
                if os.path.exists(temp_audio_path):
                    os.remove(temp_audio_path)

        except Exception as e:
            return {'success': False, 'error': f'Audio processing failed: {str(e)}'}

    def _solve_with_2captcha(self, base64_string):
        """
        Solve captcha using 2captcha.com service.
        """
        api_key = self.api_keys.get('2captcha')
        if not api_key:
            return {'success': False, 'error': '2captcha API key not provided'}

        try:
            # Submit captcha
            submit_url = "http://2captcha.com/in.php"
            data = {
                'key': api_key,
                'method': 'base64',
                'body': base64_string,
                'json': 1
            }

            response = requests.post(submit_url, data=data, timeout=30)
            result = response.json()

            if result.get('status') != 1:
                return {'success': False, 'error': f'2captcha submit failed: {result.get("error_text", "Unknown error")}'}

            captcha_id = result.get('request')

            # Wait for solution
            result_url = "http://2captcha.com/res.php"
            max_attempts = 30  # 5 minutes max

            for attempt in range(max_attempts):
                time.sleep(10)  # Wait 10 seconds between checks

                params = {
                    'key': api_key,
                    'action': 'get',
                    'id': captcha_id,
                    'json': 1
                }

                response = requests.get(result_url, params=params, timeout=30)
                result = response.json()

                if result.get('status') == 1:
                    return {
                        'success': True,
                        'text': result.get('request', ''),
                        'method': '2captcha',
                        'confidence': 0.95,
                        'cost': 0.002  # Approximate cost in USD
                    }
                elif result.get('error_text') and result.get('error_text') != 'CAPCHA_NOT_READY':
                    return {'success': False, 'error': f'2captcha error: {result.get("error_text")}'}

            return {'success': False, 'error': '2captcha timeout'}

        except Exception as e:
            return {'success': False, 'error': f'2captcha exception: {str(e)}'}

    def _solve_with_anticaptcha(self, base64_string):
        """
        Solve captcha using anti-captcha.com service.
        """
        api_key = self.api_keys.get('anticaptcha')
        if not api_key:
            return {'success': False, 'error': 'Anti-captcha API key not provided'}

        try:
            # Create task
            create_task_url = "https://api.anti-captcha.com/createTask"
            task_data = {
                "clientKey": api_key,
                "task": {
                    "type": "ImageToTextTask",
                    "body": base64_string
                }
            }

            response = requests.post(create_task_url, json=task_data, timeout=30)
            result = response.json()

            if result.get('errorId') != 0:
                return {'success': False, 'error': f'Anti-captcha create task failed: {result.get("errorDescription")}'}

            task_id = result.get('taskId')

            # Wait for solution
            get_result_url = "https://api.anti-captcha.com/getTaskResult"
            max_attempts = 30

            for attempt in range(max_attempts):
                time.sleep(10)

                result_data = {
                    "clientKey": api_key,
                    "taskId": task_id
                }

                response = requests.post(get_result_url, json=result_data, timeout=30)
                result = response.json()

                if result.get('status') == 'ready':
                    solution = result.get('solution', {})
                    return {
                        'success': True,
                        'text': solution.get('text', ''),
                        'method': 'anticaptcha',
                        'confidence': 0.96,
                        'cost': 0.002
                    }
                elif result.get('errorId') != 0:
                    return {'success': False, 'error': f'Anti-captcha error: {result.get("errorDescription")}'}

            return {'success': False, 'error': 'Anti-captcha timeout'}

        except Exception as e:
            return {'success': False, 'error': f'Anti-captcha exception: {str(e)}'}

    def _solve_with_deathbycaptcha(self, base64_string):
        """
        Solve captcha using deathbycaptcha.com service.
        """
        username = self.api_keys.get('dbc_username')
        password = self.api_keys.get('dbc_password')

        if not username or not password:
            return {'success': False, 'error': 'DeathByCaptcha credentials not provided'}

        try:
            # Submit captcha
            submit_url = "http://api.dbcapi.me/api/captcha"

            # Convert base64 to file-like object
            image_data = base64.b64decode(base64_string)

            files = {'captchafile': ('captcha.png', BytesIO(image_data), 'image/png')}
            data = {
                'username': username,
                'password': password
            }

            response = requests.post(submit_url, files=files, data=data, timeout=30)
            result = response.json()

            if not result.get('captcha'):
                return {'success': False, 'error': f'DeathByCaptcha submit failed: {result.get("error", "Unknown error")}'}

            captcha_id = result.get('captcha')

            # Wait for solution
            max_attempts = 30

            for attempt in range(max_attempts):
                time.sleep(10)

                status_url = f"http://api.dbcapi.me/api/captcha/{captcha_id}"
                response = requests.get(status_url, timeout=30)
                result = response.json()

                if result.get('text'):
                    return {
                        'success': True,
                        'text': result.get('text', ''),
                        'method': 'deathbycaptcha',
                        'confidence': 0.93,
                        'cost': 0.00139
                    }
                elif result.get('is_correct') is False:
                    return {'success': False, 'error': 'DeathByCaptcha failed to solve'}

            return {'success': False, 'error': 'DeathByCaptcha timeout'}

        except Exception as e:
            return {'success': False, 'error': f'DeathByCaptcha exception: {str(e)}'}

    def _update_stats(self, method, success):
        """Update success statistics for methods."""
        if method in self.method_stats:
            self.method_stats[method]['attempts'] += 1
            if success:
                self.method_stats[method]['success'] += 1

    def get_stats(self):
        """Get success statistics for all methods."""
        stats = {}
        for method, data in self.method_stats.items():
            attempts = data['attempts']
            success = data['success']
            success_rate = (success / attempts * 100) if attempts > 0 else 0
            stats[method] = {
                'attempts': attempts,
                'success': success,
                'success_rate': f"{success_rate:.1f}%"
            }
        return stats

    def set_api_keys(self, api_keys):
        """Set API keys for paid services."""
        self.api_keys.update(api_keys)

    def test_services(self):
        """Test all available captcha solving services."""
        test_results = {}

        # Test 2captcha
        if self.api_keys.get('2captcha'):
            try:
                balance_url = "http://2captcha.com/res.php"
                params = {'key': self.api_keys['2captcha'], 'action': 'getbalance'}
                response = requests.get(balance_url, params=params, timeout=10)
                if response.text.startswith('ERROR'):
                    test_results['2captcha'] = {'status': 'error', 'message': response.text}
                else:
                    test_results['2captcha'] = {'status': 'ok', 'balance': f"${response.text}"}
            except Exception as e:
                test_results['2captcha'] = {'status': 'error', 'message': str(e)}
        else:
            test_results['2captcha'] = {'status': 'no_key', 'message': 'API key not provided'}

        # Test Anti-captcha
        if self.api_keys.get('anticaptcha'):
            try:
                balance_url = "https://api.anti-captcha.com/getBalance"
                data = {"clientKey": self.api_keys['anticaptcha']}
                response = requests.post(balance_url, json=data, timeout=10)
                result = response.json()
                if result.get('errorId') == 0:
                    test_results['anticaptcha'] = {'status': 'ok', 'balance': f"${result.get('balance', 0)}"}
                else:
                    test_results['anticaptcha'] = {'status': 'error', 'message': result.get('errorDescription')}
            except Exception as e:
                test_results['anticaptcha'] = {'status': 'error', 'message': str(e)}
        else:
            test_results['anticaptcha'] = {'status': 'no_key', 'message': 'API key not provided'}

        # Test DeathByCaptcha
        if self.api_keys.get('dbc_username') and self.api_keys.get('dbc_password'):
            try:
                balance_url = "http://api.dbcapi.me/api/user"
                data = {
                    'username': self.api_keys['dbc_username'],
                    'password': self.api_keys['dbc_password']
                }
                response = requests.post(balance_url, data=data, timeout=10)
                result = response.json()
                if 'balance' in result:
                    test_results['deathbycaptcha'] = {'status': 'ok', 'balance': f"${result.get('balance', 0)}"}
                else:
                    test_results['deathbycaptcha'] = {'status': 'error', 'message': 'Authentication failed'}
            except Exception as e:
                test_results['deathbycaptcha'] = {'status': 'error', 'message': str(e)}
        else:
            test_results['deathbycaptcha'] = {'status': 'no_key', 'message': 'Credentials not provided'}

        return test_results


# Legacy compatibility - keep the old class name as alias
AdvancedCaptchaSolver = NonOCRCaptchaSolver





