#!/usr/bin/env python3
"""
Enhanced Stealth Aadhaar Validator with Manual Captcha Input

This script provides the full stealth validation experience with manual captcha input
as the primary solving method, ensuring 100% accuracy while maintaining stealth.
"""

import sys
import os
import time
from datetime import datetime
from stealth_a<PERSON>haar_checker import Stealth<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def print_banner():
    """Print the application banner."""
    print("🧩 STEALTH AADHAAR VALIDATOR - MANUAL MODE")
    print("=" * 60)
    print("Features enabled:")
    print("✅ Undetected Chrome browser")
    print("✅ Human behavior simulation")
    print("✅ Manual captcha input (100% accuracy)")
    print("✅ Reduced captcha frequency")
    print("✅ Natural typing and clicking patterns")
    print("✅ Realistic mouse movements")
    print("✅ Session break simulation")
    print("=" * 60)


def check_setup():
    """Check if the stealth setup is working."""
    print("\n🔍 Checking Stealth Setup...")
    
    try:
        # Check if required modules are available
        import undetected_chromedriver
        print("Undetected ChromeDriver: ✅ Available")
    except ImportError:
        print("Undetected ChromeDriver: ❌ Missing")
        return False
    
    try:
        import speech_recognition
        print("Speech Recognition: ✅ Available")
    except ImportError:
        print("Speech Recognition: ⚠️ Missing (audio captcha disabled)")
    
    try:
        import tkinter
        print("GUI Support: ✅ Available")
    except ImportError:
        print("GUI Support: ⚠️ Missing (console fallback only)")
    
    print("✅ Stealth setup verified!")
    return True


def get_user_preferences():
    """Get user preferences for the validation."""
    print("\n🎭 Choose Human Behavior Personality:")
    print("1. Fast - Quick typing, impatient behavior")
    print("2. Normal - Average human behavior (recommended)")
    print("3. Slow - Careful, deliberate behavior")
    print("4. Elderly - Very careful, slower behavior")
    
    while True:
        try:
            choice = input("\nEnter choice (1-4): ").strip()
            personalities = {
                '1': 'fast',
                '2': 'normal', 
                '3': 'slow',
                '4': 'elderly'
            }
            
            if choice in personalities:
                personality = personalities[choice]
                print(f"Selected: {personality.title()} personality")
                break
            else:
                print("❌ Invalid choice. Please enter 1-4.")
        except KeyboardInterrupt:
            print("\n❌ Setup cancelled")
            sys.exit(1)
    
    print("\nChoose browser mode:")
    print("1. Visible browser (recommended for manual captcha)")
    print("2. Headless mode (faster, but harder to see captcha)")
    
    while True:
        try:
            choice = input("Enter choice (1 or 2): ").strip()
            if choice == '1':
                headless = False
                print("Selected: Visible browser")
                break
            elif choice == '2':
                headless = True
                print("Selected: Headless mode")
                break
            else:
                print("❌ Invalid choice. Please enter 1 or 2.")
        except KeyboardInterrupt:
            print("\n❌ Setup cancelled")
            sys.exit(1)
    
    return personality, headless


def get_aadhaar_number():
    """Get Aadhaar number from user."""
    print("\n📱 Aadhaar Number Input:")
    print("💡 You can use the test number: ************")
    
    while True:
        try:
            aadhaar = input("Enter Aadhaar number (or 'test' for test number): ").strip()
            
            if aadhaar.lower() == 'test':
                aadhaar = "************"
                print(f"Using test Aadhaar number: {aadhaar}")
                break
            elif len(aadhaar) == 12 and aadhaar.isdigit():
                break
            else:
                print("❌ Invalid Aadhaar number. Must be 12 digits.")
        except KeyboardInterrupt:
            print("\n❌ Input cancelled")
            sys.exit(1)
    
    return aadhaar


def show_manual_captcha_instructions():
    """Show instructions for manual captcha input."""
    print("\n" + "🧩 MANUAL CAPTCHA INSTRUCTIONS")
    print("=" * 50)
    print("When a captcha appears, you will see:")
    print("📱 A GUI window with the captcha image")
    print("⌨️  A text input field")
    print("✅ Submit and Skip buttons")
    print()
    print("How to use:")
    print("1. 👀 Look at the captcha image carefully")
    print("2. ⌨️  Type the text exactly as shown")
    print("3. ✅ Press Enter or click Submit")
    print("4. ⏭️  Click Skip if captcha is unclear")
    print()
    print("💡 Tips:")
    print("• 🔍 Image is automatically scaled for clarity")
    print("• 🔤 Pay attention to case sensitivity")
    print("• 🔄 System will retry if submission fails")
    print("• 🆓 This method is 100% free and accurate")
    print("=" * 50)


def run_validation(aadhaar, personality, headless):
    """Run the Aadhaar validation with manual captcha input."""
    print(f"\n🚀 Starting Stealth Validation...")
    print(f"Aadhaar: {aadhaar}")
    print(f"Personality: {personality}")
    print(f"Mode: {'Visible' if not headless else 'Headless'}")
    
    try:
        # Initialize stealth checker
        print("\n🚀 Initializing Stealth Aadhaar Checker...")
        checker = StealthAadhaarChecker(
            debug=True,
            headless=headless,
            personality=personality
        )
        
        print("✅ All components initialized successfully")
        
        # Show process overview
        print("\n📋 Stealth Process:")
        print("1. 🌐 Stealth navigation with human behavior")
        print("2. 👀 Natural page exploration and reading")
        print("3. 🧩 Captcha detection and manual input")
        print("4. ⌨️ Human-like form filling")
        print("5. 🎯 Patient result extraction")
        
        # Start validation
        start_time = time.time()
        result = checker.validate_aadhaar(aadhaar)
        end_time = time.time()
        
        # Display results
        print("\n" + "=" * 60)
        print("📊 STEALTH VALIDATION RESULTS")
        print("=" * 60)
        
        if result.get('success'):
            print("✅ STATUS: SUCCESS")
            print(f"📱 Aadhaar: {aadhaar}")
            print(f"✅ Valid: {result.get('valid', 'Unknown')}")
            print(f"📄 Details: {result.get('details', 'N/A')}")
            print(f"⏰ Time taken: {end_time - start_time:.1f} seconds")
        else:
            print("❌ STATUS: FAILED")
            print(f"📱 Aadhaar: {aadhaar}")
            print(f"❗ Error: {result.get('error', 'Unknown error')}")
            print(f"🏷️ Error Type: {result.get('error_type', 'Unknown')}")
            print(f"⏰ Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Show statistics
        stats = checker.get_stats()
        print(f"\n📊 Final Stealth Statistics:")
        for key, value in stats.items():
            if isinstance(value, float):
                print(f"   {key}: {value:.1f}%")
            else:
                print(f"   {key}: {value}")
        
        # Show recommendations
        if not result.get('success'):
            print(f"\n💡 RECOMMENDATIONS:")
            print(f"   🧩 Manual captcha input provides 100% accuracy")
            print(f"   🔄 Try again - captcha images vary in difficulty")
            print(f"   👁️ Use visible mode for easier captcha viewing")
        
        return result.get('success', False)
        
    except KeyboardInterrupt:
        print("\n❌ Validation cancelled by user")
        return False
    except Exception as e:
        print(f"\n❌ Validation failed: {str(e)}")
        return False


def main():
    """Main function."""
    print_banner()
    
    # Check setup
    if not check_setup():
        print("\n❌ Setup check failed. Please install required dependencies.")
        sys.exit(1)
    
    # Get user preferences
    personality, headless = get_user_preferences()
    
    # Get Aadhaar number
    aadhaar = get_aadhaar_number()
    
    # Show manual captcha instructions
    show_manual_captcha_instructions()
    
    # Confirm start
    try:
        input("\n⏸️ Press Enter to start validation...")
    except KeyboardInterrupt:
        print("\n❌ Validation cancelled")
        sys.exit(1)
    
    # Run validation
    success = run_validation(aadhaar, personality, headless)
    
    # Final message
    print("\n" + "🎉 VALIDATION COMPLETE")
    print("=" * 60)
    
    if success:
        print("✅ Stealth validation completed successfully!")
        print("🎯 Manual captcha input provided 100% accuracy")
    else:
        print("⚠️ Validation completed with issues")
        print("🔧 Stealth validation needs attention.")
        print("Run 'python test_stealth_features.py' for detailed diagnostics.")
    
    print("\n💡 Manual Mode Benefits:")
    print("   🎯 100% captcha solving accuracy")
    print("   🆓 Completely free (no API costs)")
    print("   🕵️ Full stealth behavior maintained")
    print("   ⚡ Fast and reliable")
    print("   🔧 Perfect for development and testing")


if __name__ == "__main__":
    main()
