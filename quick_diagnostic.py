#!/usr/bin/env python3
"""
Quick diagnostic to identify remaining bottlenecks
"""

import sys
import os
import time

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_initialization_speed():
    """Test each component initialization speed"""
    
    print("🔍 INITIALIZATION SPEED DIAGNOSTIC")
    print("=" * 60)
    
    # Test 1: Import speed
    start = time.time()
    from stealth_aadhaar_checker import StealthAadhaarChecker
    import_time = time.time() - start
    print(f"📦 Import time: {import_time:.2f}s")
    
    # Test 2: Object creation speed
    start = time.time()
    checker = StealthAadhaarChecker(debug=False)  # No debug to reduce output
    init_time = time.time() - start
    print(f"🚀 Initialization time: {init_time:.2f}s")
    
    # Test 3: Browser readiness
    start = time.time()
    # Just check if browser is ready
    try:
        current_url = checker.stealth_browser.driver.current_url
        browser_ready_time = time.time() - start
        print(f"🌐 Browser ready time: {browser_ready_time:.2f}s")
        print(f"📍 Current URL: {current_url}")
    except Exception as e:
        print(f"❌ Browser not ready: {e}")
    
    # Test 4: Navigation speed test
    start = time.time()
    try:
        checker.stealth_browser.driver.get("https://www.google.com")
        nav_time = time.time() - start
        print(f"🚀 Navigation test time: {nav_time:.2f}s")
    except Exception as e:
        print(f"❌ Navigation test failed: {e}")
    
    # Close
    checker.close()
    
    total_time = import_time + init_time
    print("-" * 60)
    print(f"📊 TOTAL STARTUP TIME: {total_time:.2f}s")
    
    if total_time < 5:
        print("✅ STARTUP SPEED: EXCELLENT")
    elif total_time < 10:
        print("⚠️ STARTUP SPEED: GOOD")
    else:
        print("❌ STARTUP SPEED: NEEDS IMPROVEMENT")

if __name__ == "__main__":
    test_initialization_speed()
