#!/usr/bin/env python3
"""
Debug Captcha Detection Script

This script helps identify the current captcha selectors and structure
on the UIDAI website to fix captcha detection issues.
"""

import time
import json
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON><PERSON><PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from stealth_browser import StealthBrowser
from config import UIDAI_URL, SELECTORS


def analyze_page_structure(driver):
    """Analyze the page structure to find captcha elements."""
    print("🔍 Analyzing page structure...")
    
    # Get page source for analysis
    page_source = driver.page_source
    
    # Look for common captcha-related terms in the HTML
    captcha_terms = [
        'captcha', 'Captcha', 'CAPTCHA',
        'verification', 'Verification',
        'code', 'Code',
        'image', 'canvas',
        'data:image', 'base64'
    ]
    
    found_terms = {}
    for term in captcha_terms:
        count = page_source.lower().count(term.lower())
        if count > 0:
            found_terms[term] = count
    
    print(f"📊 Found captcha-related terms: {found_terms}")
    
    # Look for all images on the page
    images = driver.find_elements(By.TAG_NAME, "img")
    print(f"🖼️ Found {len(images)} images on page")
    
    for i, img in enumerate(images):
        try:
            src = img.get_attribute('src')
            alt = img.get_attribute('alt')
            class_name = img.get_attribute('class')
            id_attr = img.get_attribute('id')
            
            if src and ('captcha' in src.lower() or 'data:image' in src.lower()):
                print(f"  📸 Image {i+1}: Potential captcha")
                print(f"    src: {src[:100]}...")
                print(f"    alt: {alt}")
                print(f"    class: {class_name}")
                print(f"    id: {id_attr}")
        except:
            continue
    
    # Look for canvas elements
    canvases = driver.find_elements(By.TAG_NAME, "canvas")
    print(f"🎨 Found {len(canvases)} canvas elements")
    
    for i, canvas in enumerate(canvases):
        try:
            class_name = canvas.get_attribute('class')
            id_attr = canvas.get_attribute('id')
            print(f"  🎨 Canvas {i+1}: class='{class_name}', id='{id_attr}'")
        except:
            continue


def test_captcha_selectors(driver):
    """Test all configured captcha selectors."""
    print("\n🧪 Testing configured captcha selectors...")
    
    selector_groups = {
        'captcha_images': SELECTORS['captcha_images'],
        'captcha_canvas': SELECTORS['captcha_canvas'],
        'captcha_containers': SELECTORS['captcha_containers']
    }
    
    results = {}
    
    for group_name, selectors in selector_groups.items():
        print(f"\n📋 Testing {group_name}:")
        group_results = []
        
        for selector in selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"  ✅ {selector} - Found {len(elements)} element(s)")
                    for i, elem in enumerate(elements):
                        try:
                            visible = elem.is_displayed()
                            enabled = elem.is_enabled()
                            tag = elem.tag_name
                            print(f"    Element {i+1}: {tag}, visible={visible}, enabled={enabled}")
                            
                            if tag == 'img':
                                src = elem.get_attribute('src')
                                if src:
                                    print(f"      src: {src[:100]}...")
                        except:
                            pass
                    group_results.append({'selector': selector, 'count': len(elements), 'found': True})
                else:
                    print(f"  ❌ {selector} - No elements found")
                    group_results.append({'selector': selector, 'count': 0, 'found': False})
            except Exception as e:
                print(f"  ⚠️ {selector} - Error: {str(e)}")
                group_results.append({'selector': selector, 'error': str(e), 'found': False})
        
        results[group_name] = group_results
    
    return results


def find_all_inputs(driver):
    """Find all input fields on the page."""
    print("\n📝 Analyzing input fields...")
    
    inputs = driver.find_elements(By.TAG_NAME, "input")
    print(f"Found {len(inputs)} input fields:")
    
    for i, input_elem in enumerate(inputs):
        try:
            input_type = input_elem.get_attribute('type')
            name = input_elem.get_attribute('name')
            id_attr = input_elem.get_attribute('id')
            placeholder = input_elem.get_attribute('placeholder')
            class_name = input_elem.get_attribute('class')
            visible = input_elem.is_displayed()
            
            print(f"  📝 Input {i+1}:")
            print(f"    type: {input_type}")
            print(f"    name: {name}")
            print(f"    id: {id_attr}")
            print(f"    placeholder: {placeholder}")
            print(f"    class: {class_name}")
            print(f"    visible: {visible}")
            print()
        except:
            continue


def extract_page_info(driver):
    """Extract comprehensive page information."""
    print("\n📄 Extracting page information...")
    
    try:
        title = driver.title
        url = driver.current_url
        print(f"Title: {title}")
        print(f"URL: {url}")
        
        # Look for forms
        forms = driver.find_elements(By.TAG_NAME, "form")
        print(f"Found {len(forms)} forms")
        
        for i, form in enumerate(forms):
            try:
                action = form.get_attribute('action')
                method = form.get_attribute('method')
                class_name = form.get_attribute('class')
                print(f"  Form {i+1}: action='{action}', method='{method}', class='{class_name}'")
            except:
                continue
        
        # Look for buttons
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"Found {len(buttons)} buttons")
        
        for i, button in enumerate(buttons):
            try:
                text = button.text
                button_type = button.get_attribute('type')
                class_name = button.get_attribute('class')
                onclick = button.get_attribute('onclick')
                visible = button.is_displayed()
                
                if text or 'submit' in str(button_type).lower():
                    print(f"  Button {i+1}: '{text}', type='{button_type}', class='{class_name}', visible={visible}")
                    if onclick:
                        print(f"    onclick: {onclick}")
            except:
                continue
                
    except Exception as e:
        print(f"Error extracting page info: {e}")


def save_debug_info(driver, results):
    """Save debug information to files."""
    print("\n💾 Saving debug information...")
    
    try:
        # Save page source
        with open("debug_page_source.html", "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        print("✅ Saved page source to debug_page_source.html")
        
        # Save selector test results
        with open("debug_selector_results.json", "w") as f:
            json.dump(results, f, indent=2)
        print("✅ Saved selector results to debug_selector_results.json")
        
        # Take screenshot
        driver.save_screenshot("debug_screenshot.png")
        print("✅ Saved screenshot to debug_screenshot.png")
        
    except Exception as e:
        print(f"⚠️ Error saving debug info: {e}")


def suggest_new_selectors(driver):
    """Suggest new selectors based on page analysis."""
    print("\n💡 Suggesting new selectors...")
    
    suggestions = {
        'captcha_images': [],
        'captcha_containers': [],
        'aadhaar_input': [],
        'captcha_input': [],
        'submit_button': []
    }
    
    try:
        # Look for images with captcha-like characteristics
        images = driver.find_elements(By.TAG_NAME, "img")
        for img in images:
            src = img.get_attribute('src') or ''
            alt = img.get_attribute('alt') or ''
            class_name = img.get_attribute('class') or ''
            
            if any(term in (src + alt + class_name).lower() for term in ['captcha', 'verification', 'code']):
                # Build selector
                if class_name:
                    suggestions['captcha_images'].append(f"img.{class_name.replace(' ', '.')}")
                if img.get_attribute('id'):
                    suggestions['captcha_images'].append(f"img#{img.get_attribute('id')}")
        
        # Look for input fields
        inputs = driver.find_elements(By.TAG_NAME, "input")
        for input_elem in inputs:
            name = input_elem.get_attribute('name') or ''
            id_attr = input_elem.get_attribute('id') or ''
            placeholder = input_elem.get_attribute('placeholder') or ''
            class_name = input_elem.get_attribute('class') or ''
            input_type = input_elem.get_attribute('type') or ''
            
            # Check for Aadhaar input
            if any(term in (name + id_attr + placeholder).lower() for term in ['aadhaar', 'uid', 'number']):
                if id_attr:
                    suggestions['aadhaar_input'].append(f"input#{id_attr}")
                if name:
                    suggestions['aadhaar_input'].append(f"input[name='{name}']")
                if class_name:
                    suggestions['aadhaar_input'].append(f"input.{class_name.replace(' ', '.')}")
            
            # Check for captcha input
            if any(term in (name + id_attr + placeholder).lower() for term in ['captcha', 'verification', 'code']):
                if id_attr:
                    suggestions['captcha_input'].append(f"input#{id_attr}")
                if name:
                    suggestions['captcha_input'].append(f"input[name='{name}']")
                if class_name:
                    suggestions['captcha_input'].append(f"input.{class_name.replace(' ', '.')}")
        
        # Look for submit buttons
        buttons = driver.find_elements(By.TAG_NAME, "button")
        for button in buttons:
            text = button.text or ''
            button_type = button.get_attribute('type') or ''
            class_name = button.get_attribute('class') or ''
            
            if 'submit' in button_type.lower() or any(term in text.lower() for term in ['submit', 'verify', 'check', 'validate']):
                if class_name:
                    suggestions['submit_button'].append(f"button.{class_name.replace(' ', '.')}")
                if button.get_attribute('id'):
                    suggestions['submit_button'].append(f"button#{button.get_attribute('id')}")
        
        # Remove duplicates
        for key in suggestions:
            suggestions[key] = list(set(suggestions[key]))
        
        print("📋 Suggested new selectors:")
        for category, selectors in suggestions.items():
            if selectors:
                print(f"  {category}:")
                for selector in selectors[:5]:  # Show top 5
                    print(f"    {selector}")
        
        return suggestions
        
    except Exception as e:
        print(f"⚠️ Error generating suggestions: {e}")
        return suggestions


def main():
    """Main debug function."""
    print("🔍 Captcha Detection Debug Tool")
    print("=" * 50)
    
    try:
        with StealthBrowser(headless=False, debug=True) as browser:
            print(f"🌐 Navigating to: {UIDAI_URL}")
            browser.human_navigate(UIDAI_URL)
            
            # Wait for page to load
            time.sleep(5)
            
            # Analyze page structure
            analyze_page_structure(browser.driver)
            
            # Test current selectors
            results = test_captcha_selectors(browser.driver)
            
            # Find all inputs
            find_all_inputs(browser.driver)
            
            # Extract page info
            extract_page_info(browser.driver)
            
            # Suggest new selectors
            suggestions = suggest_new_selectors(browser.driver)
            
            # Save debug information
            save_debug_info(browser.driver, {
                'selector_results': results,
                'suggestions': suggestions
            })
            
            print("\n" + "=" * 50)
            print("🎯 Debug Analysis Complete!")
            print("=" * 50)
            print("Files created:")
            print("- debug_page_source.html (full page HTML)")
            print("- debug_selector_results.json (selector test results)")
            print("- debug_screenshot.png (page screenshot)")
            print("\nReview these files to understand the current page structure.")
            
            # Keep browser open for manual inspection
            input("\n⏸️ Press Enter to close browser and continue...")
            
    except Exception as e:
        print(f"❌ Debug analysis failed: {str(e)}")
        return False
    
    return True


if __name__ == "__main__":
    main()
