#!/usr/bin/env python3
"""
Demo Manual Captcha Input

This script demonstrates the manual captcha input feature using a sample captcha image.
"""

import base64
from PIL import Image
from io import BytesIO
from advanced_captcha_solver import NonOCRCaptchaSolver


def create_sample_captcha():
    """Create a sample captcha image for demonstration."""
    # This is a sample base64 captcha image (a simple text image)
    sample_captcha_b64 = """
iVBORw0KGgoAAAANSUhEUgAAAMgAAAAyCAYAAAAZUZThAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAAdgAAAHYBTnsmCAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANCSURBVHic7ZzNaxNBFMafJBpttVpbW6u1tlqrtbVaW6u1tVpbq7W1WlurtbVaW6u1tVpbq7W1WlurtbVaW6u1tVpbq7W1WlurtbVaW6u1tVpbq7W1WlurtbVaW6u1tVpbq7W1WlurtbVa
"""

    # For demo, we'll create a simple text image
    from PIL import Image, ImageDraw, ImageFont

    # Create a simple captcha-like image
    img = Image.new('RGB', (200, 60), color='white')
    draw = ImageDraw.Draw(img)

    # Try to use a font, fallback to default if not available
    try:
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        font = ImageFont.load_default()

    # Draw some text
    text = "ABC123"
    draw.text((20, 15), text, fill='black', font=font)

    # Add some noise lines
    import random
    for _ in range(5):
        x1, y1 = random.randint(0, 200), random.randint(0, 60)
        x2, y2 = random.randint(0, 200), random.randint(0, 60)
        draw.line([(x1, y1), (x2, y2)], fill='gray', width=1)

    # Convert to base64
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    img_data = buffer.getvalue()
    b64_string = base64.b64encode(img_data).decode('utf-8')

    return b64_string, img


def demo_manual_input():
    """Demonstrate the manual captcha input feature."""
    print("🧩 MANUAL CAPTCHA INPUT DEMO")
    print("=" * 50)
    print()
    print("This demo shows how the manual captcha input works.")
    print("You'll see a GUI window with a sample captcha image.")
    print("The correct answer for this demo captcha is: ABC123")
    print()

    try:
        # Create sample captcha
        print("📸 Creating sample captcha image...")
        b64_string, img = create_sample_captcha()

        # Initialize the captcha solver
        print("🔧 Initializing captcha solver...")
        solver = NonOCRCaptchaSolver(debug=True)

        print("✅ Captcha solver ready!")
        print()
        print("🎯 Starting manual input demo...")
        print("💡 A GUI window will open showing the captcha")
        print("⌨️  Enter 'ABC123' when prompted")
        print()

        # Try manual input
        result = solver._try_manual_input(b64_string, img)

        print("\n📊 DEMO RESULTS:")
        print("=" * 30)

        if result.get('success'):
            entered_text = result.get('text', '')
            print(f"✅ Success: {result.get('success')}")
            print(f"📝 Text entered: '{entered_text}'")
            print(f"🎯 Method: {result.get('method')}")
            print(f"📊 Confidence: {result.get('confidence', 'N/A')}")

            if entered_text == "ABC123":
                print("🎉 Perfect! You entered the correct text!")
            else:
                print(f"⚠️  Expected 'ABC123', got '{entered_text}'")
        else:
            print(f"❌ Failed: {result.get('error', 'Unknown error')}")
            print(f"🎯 Method: {result.get('method')}")

        print("\n💡 Manual Input Features Demonstrated:")
        print("   ✅ GUI-based captcha display")
        print("   ✅ Image scaling for better visibility")
        print("   ✅ User-friendly input interface")
        print("   ✅ Submit and skip options")
        print("   ✅ Keyboard shortcuts (Enter to submit)")
        print("   ✅ Console fallback if GUI fails")

        return result.get('success', False)

    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")
        return False


def show_integration_info():
    """Show information about integrating manual input with stealth validation."""
    print("\n🔗 INTEGRATION WITH STEALTH VALIDATION")
    print("=" * 50)
    print()
    print("The manual captcha input is now integrated into your stealth system:")
    print()
    print("📋 How it works:")
    print("1. 🕵️ Stealth browser navigates to UIDAI website")
    print("2. 🤖 Human behavior simulation types Aadhaar number")
    print("3. 🧩 System detects and extracts captcha image")
    print("4. 🖼️ Manual input GUI opens automatically")
    print("5. 👤 You enter the captcha text")
    print("6. ✅ System submits form and gets results")
    print()
    print("🎯 Benefits:")
    print("   • 🎯 100% captcha solving accuracy")
    print("   • 🆓 Completely free (no API costs)")
    print("   • 🕵️ Maintains all stealth features")
    print("   • ⚡ Fast and reliable")
    print("   • 🔧 Perfect for development and testing")
    print()
    print("🚀 To use with stealth validation:")
    print("   python run_stealth_aadhaar_manual.py")
    print()
    print("🧪 To test just the manual input:")
    print("   python demo_manual_captcha.py")


def main():
    """Main function."""
    print("🎮 MANUAL CAPTCHA INPUT DEMONSTRATION")
    print("=" * 60)
    print()
    print("This demo shows the manual captcha input feature that")
    print("allows you to manually enter captcha text while maintaining")
    print("all stealth and human behavior features.")
    print()

    try:
        response = input("🎯 Start manual captcha demo? (y/n): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ Demo cancelled")
            return
    except KeyboardInterrupt:
        print("\n❌ Demo cancelled")
        return

    print()

    # Run the demo
    success = demo_manual_input()

    print("\n🎉 DEMO COMPLETE")
    print("=" * 30)

    if success:
        print("✅ Manual captcha input demo completed successfully!")
    else:
        print("⚠️ Demo completed with issues")

    # Show integration information
    show_integration_info()


if __name__ == "__main__":
    main()