#!/usr/bin/env python3
"""
Human Behavior Simulation Module

This module provides realistic human behavior patterns for web automation,
including timing patterns, interaction styles, and behavioral fingerprints.
"""

import time
import random
import math
from datetime import datetime, timedelta
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys


class HumanBehaviorSimulator:
    """
    Simulates realistic human behavior patterns for web automation.
    """
    
    def __init__(self, driver, personality="normal"):
        self.driver = driver
        self.action_chains = ActionChains(driver)
        self.personality = personality
        
        # Behavior profiles
        self.profiles = {
            "fast": {
                "typing_speed": (0.02, 0.08),
                "click_delay": (0.05, 0.2),
                "reading_time": (1, 3),
                "mistake_rate": 0.02,
                "impatience_factor": 1.5
            },
            "normal": {
                "typing_speed": (0.05, 0.15),
                "click_delay": (0.1, 0.4),
                "reading_time": (2, 5),
                "mistake_rate": 0.05,
                "impatience_factor": 1.0
            },
            "slow": {
                "typing_speed": (0.1, 0.3),
                "click_delay": (0.2, 0.8),
                "reading_time": (3, 8),
                "mistake_rate": 0.08,
                "impatience_factor": 0.7
            },
            "elderly": {
                "typing_speed": (0.15, 0.5),
                "click_delay": (0.3, 1.2),
                "reading_time": (5, 12),
                "mistake_rate": 0.12,
                "impatience_factor": 0.5
            }
        }
        
        self.current_profile = self.profiles.get(personality, self.profiles["normal"])
        
        # Session state
        self.session_start = datetime.now()
        self.actions_performed = 0
        self.mistakes_made = 0
        self.fatigue_level = 0.0
    
    def simulate_typing_pattern(self, element, text, clear_first=True):
        """
        Simulate realistic human typing patterns with:
        - Variable speed
        - Occasional mistakes
        - Pauses at word boundaries
        - Fatigue effects
        """
        if clear_first:
            self._clear_field_naturally(element)
        
        words = text.split()
        
        for word_idx, word in enumerate(words):
            # Add space before word (except first)
            if word_idx > 0:
                element.send_keys(" ")
                self._word_boundary_pause()
            
            # Type word with character-level simulation
            self._type_word_naturally(element, word)
            
            # Occasional mid-sentence pause (thinking)
            if random.random() < 0.1:
                self._thinking_pause()
        
        # Final review pause
        self._post_typing_pause()
    
    def _type_word_naturally(self, element, word):
        """Type a single word with natural patterns."""
        for char_idx, char in enumerate(word):
            # Simulate typing mistakes
            if self._should_make_mistake():
                self._simulate_typo(element, char)
            
            # Type the correct character
            element.send_keys(char)
            
            # Character-specific delays
            delay = self._calculate_character_delay(char, char_idx, len(word))
            time.sleep(delay)
            
            self.actions_performed += 1
    
    def _should_make_mistake(self):
        """Determine if a typing mistake should occur."""
        base_rate = self.current_profile["mistake_rate"]
        
        # Increase mistake rate with fatigue
        fatigue_multiplier = 1 + (self.fatigue_level * 2)
        
        # Decrease mistake rate as session progresses (learning)
        learning_factor = max(0.5, 1 - (self.actions_performed / 1000))
        
        adjusted_rate = base_rate * fatigue_multiplier * learning_factor
        
        return random.random() < adjusted_rate
    
    def _simulate_typo(self, element, intended_char):
        """Simulate a realistic typing mistake."""
        # Common typo patterns
        typo_patterns = {
            'a': ['s', 'q', 'w'],
            's': ['a', 'd', 'w', 'x'],
            'd': ['s', 'f', 'e', 'c'],
            'f': ['d', 'g', 'r', 'v'],
            'g': ['f', 'h', 't', 'b'],
            'h': ['g', 'j', 'y', 'n'],
            'j': ['h', 'k', 'u', 'm'],
            'k': ['j', 'l', 'i', ','],
            'l': ['k', ';', 'o', '.'],
            'q': ['w', 'a'],
            'w': ['q', 'e', 'a', 's'],
            'e': ['w', 'r', 's', 'd'],
            'r': ['e', 't', 'd', 'f'],
            't': ['r', 'y', 'f', 'g'],
            'y': ['t', 'u', 'g', 'h'],
            'u': ['y', 'i', 'h', 'j'],
            'i': ['u', 'o', 'j', 'k'],
            'o': ['i', 'p', 'k', 'l'],
            'p': ['o', 'l']
        }
        
        # Get possible typos for this character
        possible_typos = typo_patterns.get(intended_char.lower(), [intended_char])
        
        if possible_typos:
            typo_char = random.choice(possible_typos)
            
            # Type wrong character
            element.send_keys(typo_char)
            time.sleep(random.uniform(0.1, 0.3))
            
            # Realize mistake (quick pause, reduced from 0.2-0.6s to 0.1-0.3s)
            time.sleep(random.uniform(0.1, 0.3))
            
            # Correct it
            element.send_keys(Keys.BACKSPACE)
            time.sleep(random.uniform(0.1, 0.4))
            
            self.mistakes_made += 1
    
    def _calculate_character_delay(self, char, position, word_length):
        """Calculate realistic delay for typing a character."""
        base_delay = random.uniform(*self.current_profile["typing_speed"])
        
        # Adjust for character type
        if char.isdigit():
            base_delay *= 1.2  # Numbers are slightly slower
        elif char.isupper():
            base_delay *= 1.3  # Capitals require shift
        elif char in ".,!?;:":
            base_delay *= 1.1  # Punctuation
        
        # Adjust for position in word
        if position == 0:
            base_delay *= 1.1  # First character of word
        elif position == word_length - 1:
            base_delay *= 0.9  # Last character is often faster
        
        # Add fatigue effect
        fatigue_multiplier = 1 + (self.fatigue_level * 0.5)
        base_delay *= fatigue_multiplier
        
        # Add natural variation
        variation = random.uniform(0.7, 1.3)
        
        return base_delay * variation
    
    def _clear_field_naturally(self, element):
        """Clear input field with natural human behavior."""
        # Try Ctrl+A first (most common)
        if random.random() < 0.8:
            element.send_keys(Keys.CONTROL + "a")
            time.sleep(random.uniform(0.1, 0.3))
            element.send_keys(Keys.DELETE)
        else:
            # Use backspace (less common but human)
            current_value = element.get_attribute('value') or ''
            for _ in range(len(current_value)):
                element.send_keys(Keys.BACKSPACE)
                time.sleep(random.uniform(0.05, 0.15))
        
        time.sleep(random.uniform(0.1, 0.2))  # Reduced from 0.2-0.5s
    
    def _word_boundary_pause(self):
        """Pause between words (natural thinking time)."""
        base_pause = random.uniform(0.1, 0.4)
        
        # Longer pauses occasionally (thinking)
        if random.random() < 0.15:
            base_pause *= random.uniform(2, 4)
        
        time.sleep(base_pause)
    
    def _thinking_pause(self):
        """Quick pause for thinking/planning (optimized)."""
        pause_duration = random.uniform(0.3, 0.8)  # Reduced from 1.0-3.0s

        # Adjust for personality
        pause_duration *= self.current_profile["impatience_factor"]

        time.sleep(pause_duration)
    
    def _post_typing_pause(self):
        """Quick pause after completing typing (optimized review time)."""
        review_time = random.uniform(0.2, 0.5)  # Reduced from 0.5-2.0s
        time.sleep(review_time)
    
    def simulate_mouse_movement(self, target_element):
        """
        Simulate natural mouse movement to target element.
        """
        # Get current mouse position (approximate)
        current_pos = self.driver.execute_script("""
            return {
                x: window.mouseX || 0,
                y: window.mouseY || 0
            };
        """)
        
        # Get target position
        target_location = target_element.location
        target_size = target_element.size
        
        # Calculate target point (not center, more natural)
        target_x = target_location['x'] + random.randint(
            int(target_size['width'] * 0.2), 
            int(target_size['width'] * 0.8)
        )
        target_y = target_location['y'] + random.randint(
            int(target_size['height'] * 0.2), 
            int(target_size['height'] * 0.8)
        )
        
        # Create curved path
        self._move_mouse_naturally(current_pos, {'x': target_x, 'y': target_y})
    
    def _move_mouse_naturally(self, start_pos, end_pos):
        """Move mouse in a natural curved path."""
        distance = math.sqrt(
            (end_pos['x'] - start_pos['x'])**2 + 
            (end_pos['y'] - start_pos['y'])**2
        )
        
        # Number of steps based on distance
        steps = max(5, int(distance / 50))
        
        for step in range(steps):
            progress = (step + 1) / steps
            
            # Bezier curve for natural movement
            control_x = (start_pos['x'] + end_pos['x']) / 2 + random.randint(-50, 50)
            control_y = (start_pos['y'] + end_pos['y']) / 2 + random.randint(-30, 30)
            
            # Calculate point on curve
            t = progress
            x = (1-t)**2 * start_pos['x'] + 2*(1-t)*t * control_x + t**2 * end_pos['x']
            y = (1-t)**2 * start_pos['y'] + 2*(1-t)*t * control_y + t**2 * end_pos['y']
            
            # Move to calculated position
            try:
                self.action_chains.move_by_offset(
                    x - start_pos['x'], 
                    y - start_pos['y']
                ).perform()
                start_pos = {'x': x, 'y': y}
                
                # Variable speed
                time.sleep(random.uniform(0.01, 0.03))
            except:
                break
    
    def simulate_click_behavior(self, element):
        """
        Simulate natural clicking behavior.
        """
        # Move to element first
        self.simulate_mouse_movement(element)
        
        # Pre-click pause (aiming time)
        time.sleep(random.uniform(0.1, 0.4))
        
        # Occasional double-click by mistake
        if random.random() < 0.02:
            element.click()
            time.sleep(random.uniform(0.1, 0.2))
        
        # Main click
        element.click()
        
        # Post-click pause
        post_click_delay = random.uniform(*self.current_profile["click_delay"])
        time.sleep(post_click_delay)
        
        self.actions_performed += 1
    
    def simulate_reading_behavior(self, page_content_length=1000):
        """
        Simulate realistic page reading behavior.
        """
        # Calculate reading time based on content length
        words_per_minute = random.randint(200, 300)  # Average reading speed
        estimated_words = page_content_length / 5  # Rough estimate
        
        base_reading_time = (estimated_words / words_per_minute) * 60
        
        # Apply personality factor
        reading_time = base_reading_time * random.uniform(0.5, 2.0)
        reading_time = max(reading_time, self.current_profile["reading_time"][0])
        reading_time = min(reading_time, self.current_profile["reading_time"][1])
        
        # Simulate reading with scrolling
        self._simulate_reading_with_scrolling(reading_time)
    
    def _simulate_reading_with_scrolling(self, total_time):
        """Simulate reading with natural scrolling patterns."""
        scroll_intervals = random.randint(3, 8)
        interval_time = total_time / scroll_intervals
        
        for i in range(scroll_intervals):
            # Read for a portion of time
            time.sleep(interval_time * random.uniform(0.7, 1.3))
            
            # Scroll down
            if i < scroll_intervals - 1:  # Don't scroll on last interval
                scroll_amount = random.randint(100, 400)
                try:
                    self.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
                except:
                    pass
                
                # Brief pause after scrolling
                time.sleep(random.uniform(0.2, 0.6))
    
    def update_fatigue(self):
        """Update fatigue level based on session duration and actions."""
        session_duration = (datetime.now() - self.session_start).total_seconds() / 3600  # hours
        
        # Fatigue increases with time and actions
        time_fatigue = min(1.0, session_duration / 2)  # Max fatigue after 2 hours
        action_fatigue = min(0.5, self.actions_performed / 1000)  # Actions contribute to fatigue
        
        self.fatigue_level = (time_fatigue + action_fatigue) / 2
    
    def simulate_break_behavior(self):
        """Simulate taking a break (human behavior)."""
        break_duration = random.uniform(5, 30)  # 5-30 second break
        
        # During break, simulate minimal activity
        for _ in range(random.randint(1, 3)):
            time.sleep(break_duration / 3)
            
            # Occasional mouse movement during break
            if random.random() < 0.3:
                try:
                    self.action_chains.move_by_offset(
                        random.randint(-100, 100),
                        random.randint(-50, 50)
                    ).perform()
                except:
                    pass
    
    def get_behavior_stats(self):
        """Get statistics about simulated behavior."""
        session_duration = (datetime.now() - self.session_start).total_seconds()
        
        return {
            "session_duration": f"{session_duration:.1f} seconds",
            "actions_performed": self.actions_performed,
            "mistakes_made": self.mistakes_made,
            "mistake_rate": f"{(self.mistakes_made / max(1, self.actions_performed)) * 100:.1f}%",
            "fatigue_level": f"{self.fatigue_level * 100:.1f}%",
            "personality": self.personality
        }
