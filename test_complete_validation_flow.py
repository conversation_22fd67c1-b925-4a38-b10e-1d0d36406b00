#!/usr/bin/env python3
"""
Complete test of the enhanced validation flow including result extraction and display
"""

import sys
import os
import time

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stealth_aadhaar_checker import Stealth<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_complete_flow():
    """Test the complete validation flow with enhanced result display"""
    
    print("🧪 Testing Complete Enhanced Validation Flow")
    print("=" * 80)
    
    # Create checker instance
    checker = StealthAadhaarChecker(debug=True)
    
    # Test data that mimics actual UIDAI response formats
    test_scenarios = [
        {
            "name": "Standard Valid Response",
            "data": """
            ************ Exists
            Aadhaar Verification Completed
            Age Band
            20-30 years
            Gender
            MALE
            State
            Maharashtra
            Mobile
            *******671
            """
        },
        {
            "name": "Compact Valid Response", 
            "data": "************ Exists\nAadhaar Verification Completed\nAge Band: 20-30 years\nGender: MALE\nState: Maharashtra\nMobile: *******671"
        },
        {
            "name": "Invalid Response",
            "data": "************ Does Not Exist\nAadhaar Verification Failed\nInvalid Aadhaar Number"
        },
        {
            "name": "Minimal Valid Response",
            "data": "123456789012 Exists\nVerified\nAge: 25-35 years\nGender: FEMALE\nState: Delhi"
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n🧪 Test Scenario {i}: {scenario['name']}")
        print("-" * 80)
        
        # Test parsing
        parsed_result = checker._parse_validation_details(scenario['data'])
        
        # Create result structure like the main system would
        result = {
            'success': True,
            'validation_data': parsed_result
        }
        
        # Display results using the enhanced display function
        display_enhanced_result(result, scenario['name'])
        
        print("\n" + "=" * 80)
        time.sleep(1)  # Brief pause between tests
    
    checker.close()
    print("\n🎉 All test scenarios completed!")

def display_enhanced_result(result, scenario_name):
    """Enhanced result display function matching the main system"""
    print(f"\n📊 ENHANCED VALIDATION RESULTS - {scenario_name}")
    print("=" * 80)
    
    if result.get('success'):
        validation_data = result.get('validation_data', {})
        
        # Status with prominent display
        status = validation_data.get('status', 'Unknown')
        if status == 'Valid':
            print(f"✅ STATUS: {status} - AADHAAR EXISTS AND IS VERIFIED!")
        else:
            print(f"❌ STATUS: {status}")

        if validation_data.get('aadhaar_number'):
            print(f"🆔 AADHAAR NUMBER: {validation_data.get('aadhaar_number')}")

        if validation_data.get('age_band'):
            print(f"👤 AGE BAND: {validation_data.get('age_band')}")

        if validation_data.get('gender'):
            print(f"⚧️ GENDER: {validation_data.get('gender')}")

        if validation_data.get('state'):
            print(f"🏛️ STATE: {validation_data.get('state')}")

        if validation_data.get('mobile'):
            print(f"📱 MOBILE: {validation_data.get('mobile')}")

        if validation_data.get('message'):
            print(f"\n📄 COMPLETE VALIDATION MESSAGE:")
            print(f"{'='*60}")
            print(f"{validation_data.get('message')}")
            print(f"{'='*60}")
        
        print(f"\n🎉 VALIDATION COMPLETED SUCCESSFULLY!")
        
    else:
        print(f"❌ VALIDATION FAILED: {result.get('error', 'Unknown error')}")
        print("💡 Please check the Aadhaar number and try again.")
    
    print("=" * 80)

if __name__ == "__main__":
    test_complete_flow()
