#!/usr/bin/env python3
"""
Chrome Path Detection and Fix Script

This script helps detect and fix Chrome browser path issues.
"""

import os
import subprocess
import sys
from pathlib import Path


def find_chrome_paths():
    """Find all possible Chrome installation paths."""
    possible_paths = [
        # Windows paths
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', '')),
        
        # Edge (Chromium-based alternative)
        r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
        r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
        
        # Portable Chrome
        r"C:\PortableApps\GoogleChromePortable\App\Chrome-bin\chrome.exe",
    ]
    
    found_paths = []
    
    for path in possible_paths:
        if os.path.exists(path):
            found_paths.append(path)
            print(f"✅ Found Chrome at: {path}")
    
    return found_paths


def test_chrome_path(chrome_path):
    """Test if a Chrome path works with Selenium."""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        
        options = Options()
        options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.binary_location = chrome_path
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        
        # Test basic functionality
        driver.get("data:text/html,<html><body><h1>Test</h1></body></html>")
        title = driver.title
        driver.quit()
        
        print(f"✅ Chrome path works: {chrome_path}")
        return True
        
    except Exception as e:
        print(f"❌ Chrome path failed: {chrome_path} - {str(e)}")
        return False


def create_chrome_config(chrome_path):
    """Create a configuration file with the working Chrome path."""
    config_content = f'''# Chrome Configuration
# This file is auto-generated by fix_chrome_path.py

CHROME_BINARY_PATH = r"{chrome_path}"

# Use this in your stealth browser setup:
# options.binary_location = CHROME_BINARY_PATH
'''
    
    with open("chrome_config.py", "w") as f:
        f.write(config_content)
    
    print(f"✅ Created chrome_config.py with path: {chrome_path}")


def update_stealth_browser():
    """Update stealth browser to use the detected Chrome path."""
    try:
        # Read the current stealth browser file
        with open("stealth_browser.py", "r") as f:
            content = f.read()
        
        # Add Chrome path import and usage
        if "from chrome_config import CHROME_BINARY_PATH" not in content:
            # Add import at the top
            lines = content.split('\n')
            import_line = "from chrome_config import CHROME_BINARY_PATH"
            
            # Find where to insert the import
            for i, line in enumerate(lines):
                if line.startswith("from webdriver_manager"):
                    lines.insert(i + 1, import_line)
                    break
            
            # Add binary location setting
            for i, line in enumerate(lines):
                if "options.add_argument(f\"--user-agent={user_agent}\")" in line:
                    lines.insert(i + 1, "            options.binary_location = CHROME_BINARY_PATH")
                    break
            
            # Write back
            with open("stealth_browser.py", "w") as f:
                f.write('\n'.join(lines))
            
            print("✅ Updated stealth_browser.py to use detected Chrome path")
        else:
            print("✅ stealth_browser.py already configured for Chrome path")
            
    except Exception as e:
        print(f"⚠️ Could not auto-update stealth_browser.py: {e}")
        print("You may need to manually add the Chrome path to your browser options.")


def install_chrome_instructions():
    """Provide instructions for installing Chrome."""
    print("\n📋 Chrome Installation Instructions:")
    print("=" * 50)
    print("1. Download Google Chrome from: https://www.google.com/chrome/")
    print("2. Run the installer as Administrator")
    print("3. Choose 'Install for all users' if prompted")
    print("4. After installation, run this script again")
    print("\nAlternatively, you can use Microsoft Edge (Chromium-based):")
    print("- Edge is usually pre-installed on Windows 10/11")
    print("- This script can detect and use Edge as well")


def main():
    """Main function to detect and fix Chrome path issues."""
    print("🔍 Chrome Path Detection and Fix Tool")
    print("=" * 50)
    
    # Find Chrome installations
    print("🔎 Searching for Chrome installations...")
    chrome_paths = find_chrome_paths()
    
    if not chrome_paths:
        print("❌ No Chrome installations found!")
        install_chrome_instructions()
        return False
    
    print(f"\n✅ Found {len(chrome_paths)} Chrome installation(s)")
    
    # Test each path
    working_paths = []
    for path in chrome_paths:
        print(f"\n🧪 Testing Chrome path: {path}")
        if test_chrome_path(path):
            working_paths.append(path)
    
    if not working_paths:
        print("\n❌ No working Chrome paths found!")
        print("This might be due to:")
        print("- Chrome is not properly installed")
        print("- Chrome version is incompatible")
        print("- Missing dependencies")
        install_chrome_instructions()
        return False
    
    # Use the first working path
    best_path = working_paths[0]
    print(f"\n🎯 Using Chrome path: {best_path}")
    
    # Create configuration
    create_chrome_config(best_path)
    
    # Update stealth browser
    update_stealth_browser()
    
    print("\n🎉 Chrome path configuration complete!")
    print("\nNext steps:")
    print("1. Run: python quick_test.py")
    print("2. If successful, run: python run_stealth_aadhaar.py")
    
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 If you continue to have issues:")
        print("1. Try installing Chrome from: https://www.google.com/chrome/")
        print("2. Restart your computer after installation")
        print("3. Run this script again")
        sys.exit(1)
    else:
        print("\n✅ Setup complete! Your stealth browser should now work.")
        sys.exit(0)
