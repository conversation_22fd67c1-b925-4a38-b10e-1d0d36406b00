#!/usr/bin/env python3
"""
Test the optimized main script performance
"""

import sys
import os
import time

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stealth_aadhaar_checker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_optimized_main_script():
    """Test the performance of the optimized main script"""
    
    print("🚀 TESTING OPTIMIZED MAIN SCRIPT PERFORMANCE")
    print("=" * 80)
    
    # Test initialization speed
    print("🧪 Test 1: Initialization Speed")
    start_init = time.time()
    
    try:
        checker = StealthAadhaarChecker(debug=True, headless=False, personality="fast")
        end_init = time.time()
        init_time = end_init - start_init
        
        print(f"⏱️ Initialization time: {init_time:.2f} seconds")
        
        if init_time < 5:
            print("✅ EXCELLENT: Initialization under 5 seconds")
        elif init_time < 10:
            print("✅ GOOD: Initialization under 10 seconds")
        else:
            print("⚠️ SLOW: Initialization over 10 seconds")
        
        print("-" * 80)
        
        # Test navigation speed
        print("🧪 Test 2: Navigation Speed")
        start_nav = time.time()
        
        # Navigate to Google as test
        checker.stealth_browser.driver.get("https://www.google.com")
        
        end_nav = time.time()
        nav_time = end_nav - start_nav
        
        print(f"⏱️ Navigation time: {nav_time:.2f} seconds")
        
        if nav_time < 3:
            print("✅ EXCELLENT: Navigation under 3 seconds")
        elif nav_time < 5:
            print("✅ GOOD: Navigation under 5 seconds")
        else:
            print("⚠️ SLOW: Navigation over 5 seconds")
        
        print("-" * 80)
        
        # Test optimized selector retrieval
        print("🧪 Test 3: Optimized Selector Retrieval")
        start_selectors = time.time()
        
        for i in range(20):
            selectors = checker._get_optimized_selectors('aadhaar_input')
            selectors = checker._get_optimized_selectors('captcha_images')
            selectors = checker._get_optimized_selectors('submit_buttons')
        
        end_selectors = time.time()
        selector_time = end_selectors - start_selectors
        
        print(f"⏱️ 60 selector retrievals: {selector_time:.3f} seconds")
        print(f"⚡ Average per retrieval: {selector_time/60:.4f} seconds")
        
        if selector_time < 0.1:
            print("✅ EXCELLENT: Selector retrieval under 0.1 seconds")
        elif selector_time < 0.5:
            print("✅ GOOD: Selector retrieval under 0.5 seconds")
        else:
            print("⚠️ SLOW: Selector retrieval over 0.5 seconds")
        
        print("-" * 80)
        
        # Test human behavior timing
        print("🧪 Test 4: Human Behavior Timing")
        start_behavior = time.time()
        
        # Test reading behavior
        checker.human_behavior.simulate_reading_behavior(5000)  # 5KB page
        
        end_behavior = time.time()
        behavior_time = end_behavior - start_behavior
        
        print(f"⏱️ Reading behavior simulation: {behavior_time:.2f} seconds")
        
        if behavior_time < 1:
            print("✅ EXCELLENT: Reading behavior under 1 second")
        elif behavior_time < 3:
            print("✅ GOOD: Reading behavior under 3 seconds")
        else:
            print("⚠️ SLOW: Reading behavior over 3 seconds")
        
        print("-" * 80)
        
        # Close checker
        checker.close()
        
        # Calculate total time
        total_time = time.time() - start_init
        
        print("📊 OVERALL PERFORMANCE SUMMARY:")
        print(f"   🚀 Total test time: {total_time:.2f} seconds")
        print(f"   ⚡ Initialization: {init_time:.2f}s")
        print(f"   🌐 Navigation: {nav_time:.2f}s")
        print(f"   🎯 Selectors: {selector_time:.3f}s")
        print(f"   👤 Behavior: {behavior_time:.2f}s")
        
        print("-" * 80)
        
        # Performance rating
        if total_time < 10:
            print("🏆 OVERALL PERFORMANCE: EXCELLENT")
            print("🎉 All optimizations working perfectly!")
        elif total_time < 20:
            print("✅ OVERALL PERFORMANCE: VERY GOOD")
            print("🎯 Most optimizations working well!")
        elif total_time < 30:
            print("✅ OVERALL PERFORMANCE: GOOD")
            print("⚡ Some optimizations working!")
        else:
            print("⚠️ OVERALL PERFORMANCE: NEEDS IMPROVEMENT")
            print("🔧 More optimization needed!")
        
        print("=" * 80)
        
        # Expected vs actual comparison
        print("📈 OPTIMIZATION IMPACT:")
        print("   Before optimizations:")
        print("     🐌 Initialization: 30-60 seconds")
        print("     🐌 Navigation: 10-30 seconds")
        print("     🐌 Selector scanning: 27+ seconds")
        print("     🐌 Reading behavior: 5-15 seconds")
        print("     🐌 Total: 70-130+ seconds")
        print()
        print("   After optimizations:")
        print(f"     ⚡ Initialization: {init_time:.1f} seconds")
        print(f"     ⚡ Navigation: {nav_time:.1f} seconds")
        print(f"     ⚡ Selector access: {selector_time:.3f} seconds")
        print(f"     ⚡ Reading behavior: {behavior_time:.1f} seconds")
        print(f"     ⚡ Total: {total_time:.1f} seconds")
        print()
        
        if total_time < 30:
            improvement = ((90 - total_time) / 90) * 100  # Assuming 90s baseline
            print(f"   🚀 Performance improvement: {improvement:.1f}%")
            print(f"   ⚡ Speed multiplier: {90/total_time:.1f}x faster!")
        
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_optimized_main_script()
