# Core Dependencies (Python 3.13 compatible)
# Essential for Python 3.13 compatibility
setuptools>=69.0.0

selenium>=4.15.0
pytesseract>=0.3.10
Pillow>=10.2.0
requests>=2.31.0
webdriver-manager>=4.0.0
opencv-python>=4.8.0
numpy>=1.24.0

# Non-OCR Captcha Solving Dependencies
undetected-chromedriver>=3.5.0
SpeechRecognition>=3.10.0
pydub>=0.25.0

# Stealth Browser and Human Behavior Dependencies
fake-useragent>=1.4.0

# Audio processing (optional - install manually if needed)
# pyaudio>=0.2.11  # May need manual installation on some systems
# python-anticaptcha>=0.7.1  # Optional service integration
# selenium-stealth>=1.0.6  # Alternative stealth method

# Optional: For better audio processing
# ffmpeg-python>=0.2.0

# Optional: For advanced AI models (if implementing custom solutions)
# torch>=2.1.0
# torchvision>=0.16.0
# ultralytics>=8.0.0

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
