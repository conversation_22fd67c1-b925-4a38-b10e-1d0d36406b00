#!/usr/bin/env python3
"""
Test to identify the ACTUAL selectors needed for UIDAI website
This will help us hardcode the right selectors instead of wasting time scanning
"""

import sys
import os
import time

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stealth_aadhaar_checker import <PERSON>ealthA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def identify_uidai_selectors():
    """Test the actual UIDAI website to find the exact selectors we need"""
    
    print("🔍 IDENTIFYING ACTUAL UIDAI SELECTORS")
    print("=" * 80)
    
    # Create checker instance
    checker = StealthAadhaarChecker(debug=True)
    
    try:
        # Navigate to local UIDAI simulator
        print("🌐 Navigating to UIDAI form simulator...")
        start_nav = time.time()

        # Get absolute path to the HTML file
        html_file = os.path.abspath("uidai_form_simulator.html")
        file_url = f"file:///{html_file.replace(os.sep, '/')}"

        # Navigate directly using the browser
        checker.stealth_browser.driver.get(file_url)
        success = True
        if not success:
            print("❌ Failed to navigate to UIDAI form simulator")
            return
            
        nav_time = time.time() - start_nav
        print(f"✅ Navigation completed in {nav_time:.2f}s")
        
        # Wait for page to fully load
        time.sleep(3)
        
        print("\n🔍 TESTING SPECIFIC SELECTORS FOR EACH ELEMENT:")
        print("-" * 80)
        
        # Test selectors for each specific element we need
        selectors_to_test = {
            "AADHAAR INPUT FIELD": [
                "input[name='aadhaar']",
                "input[id='aadhaar']", 
                "input[placeholder*='aadhaar']",
                "input[placeholder*='Aadhaar']",
                "input[maxlength='12']",
                "input[type='text']",
                "#aadhaar",
                ".aadhaar-input",
                "input[name='uid']"
            ],
            "CAPTCHA IMAGE": [
                "img[src*='captcha']",
                "img[id*='captcha']",
                "img[alt*='captcha']",
                "#captcha",
                ".captcha-img",
                "canvas[id*='captcha']",
                "img[src*='code']"
            ],
            "CAPTCHA INPUT FIELD": [
                "input[name='captcha']",
                "input[id='captcha']",
                "input[placeholder*='captcha']",
                "input[placeholder*='code']",
                "input[maxlength='6']",
                "input[maxlength='5']",
                "input[maxlength='4']",
                "#captcha-input",
                ".captcha-input"
            ],
            "SUBMIT/PROCEED BUTTON": [
                "button[type='submit']",
                "input[type='submit']",
                "button:contains('Proceed')",
                "button:contains('Submit')",
                "button:contains('Verify')",
                "#proceed",
                "#submit",
                ".btn-primary",
                ".proceed-btn",
                ".submit-btn"
            ],
            "SUCCESS RESULT AREA": [
                ".success",
                ".result",
                ".validation-result",
                "#result",
                "#validation-result",
                ".alert-success",
                ".message-success",
                "[class*='success']",
                "[class*='result']"
            ],
            "ERROR RESULT AREA": [
                ".error",
                ".alert-error",
                ".message-error",
                "#error",
                ".alert-danger",
                ".text-danger",
                "[class*='error']",
                "[class*='invalid']"
            ]
        }
        
        # Test each category
        working_selectors = {}
        
        for category, selectors in selectors_to_test.items():
            print(f"\n🧪 Testing {category}:")
            working_selectors[category] = []
            
            for selector in selectors:
                try:
                    # Quick test with 0.5s timeout
                    checker.stealth_browser.driver.implicitly_wait(0.5)
                    elements = checker.stealth_browser.driver.find_elements(
                        checker.stealth_browser.By.CSS_SELECTOR, selector
                    )
                    
                    if elements:
                        # Check if elements are visible
                        visible_elements = [e for e in elements if e.is_displayed()]
                        if visible_elements:
                            working_selectors[category].append(selector)
                            print(f"   ✅ {selector} -> Found {len(visible_elements)} visible elements")
                        else:
                            print(f"   ⚠️ {selector} -> Found {len(elements)} elements but none visible")
                    else:
                        print(f"   ❌ {selector} -> No elements found")
                        
                except Exception as e:
                    print(f"   ❌ {selector} -> Error: {str(e)[:50]}...")
            
            # Restore normal timeout
            checker.stealth_browser.driver.implicitly_wait(10)
        
        print("\n" + "=" * 80)
        print("📊 FINAL RESULTS - WORKING SELECTORS:")
        print("=" * 80)
        
        total_working = 0
        for category, selectors in working_selectors.items():
            if selectors:
                print(f"\n✅ {category}:")
                for selector in selectors:
                    print(f"   '{selector}',")
                total_working += len(selectors)
            else:
                print(f"\n❌ {category}: No working selectors found")
        
        print(f"\n📈 Total working selectors found: {total_working}")
        
        # Generate optimized selector configuration
        print("\n" + "=" * 80)
        print("🚀 OPTIMIZED SELECTOR CONFIGURATION:")
        print("=" * 80)
        
        print("# Use these selectors directly in the script:")
        print("OPTIMIZED_SELECTORS = {")
        for category, selectors in working_selectors.items():
            key = category.lower().replace(" ", "_").replace("/", "_")
            if selectors:
                print(f"    '{key}': [")
                for selector in selectors[:3]:  # Take top 3 working selectors
                    print(f"        '{selector}',")
                print("    ],")
            else:
                print(f"    '{key}': [],  # No working selectors found")
        print("}")
        
    except Exception as e:
        print(f"❌ Error during selector identification: {e}")
    
    finally:
        checker.close()

if __name__ == "__main__":
    identify_uidai_selectors()
