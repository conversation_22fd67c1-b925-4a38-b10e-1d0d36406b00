#!/usr/bin/env python3
"""
Performance test for the optimized Aadhaar validation system
"""

import sys
import os
import time

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stealth_aadhaar_checker import <PERSON><PERSON>th<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_performance():
    """Test the performance of the optimized system"""
    
    print("⚡ PERFORMANCE TEST - OPTIMIZED AADHAAR VALIDATION SYSTEM")
    print("=" * 80)
    
    # Create checker instance
    start_init = time.time()
    checker = StealthAadhaarChecker(debug=True)
    end_init = time.time()
    
    print(f"⏱️ Initialization time: {end_init - start_init:.2f} seconds")
    print("-" * 80)
    
    # Test the parsing performance
    test_data = """
    ************ Exists
    Aadhaar Verification Completed
    Age Band
    20-30 years
    Gender
    MALE
    State
    Maharashtra
    Mobile
    *******671
    """
    
    print("🧪 Testing parsing performance...")
    start_parse = time.time()
    
    # Run parsing multiple times to test performance
    for i in range(10):
        result = checker._parse_validation_details(test_data)
        if i == 0:  # Show first result
            print(f"✅ Sample result: Status={result.get('status')}, Aadhaar={result.get('aadhaar_number')}")
    
    end_parse = time.time()
    avg_parse_time = (end_parse - start_parse) / 10
    
    print(f"⏱️ Average parsing time: {avg_parse_time:.3f} seconds")
    print(f"⚡ Parsing throughput: {10 / (end_parse - start_parse):.1f} parses/second")
    print("-" * 80)
    
    # Test submit button detection performance
    print("🧪 Testing submit button detection simulation...")
    start_detection = time.time()
    
    # Simulate the detection process (without actual browser)
    css_selectors = [
        "button[type='submit']",
        "input[type='submit']", 
        "button.btn-primary",
        "button.submit",
        ".submit-btn",
        "#submit",
        "button[class*='proceed']",
        ".proceed-btn",
        "#proceed"
    ]
    
    for selector in css_selectors:
        print(f"🔍 Simulating selector: {selector}")
        time.sleep(0.1)  # Simulate the optimized timing
    
    end_detection = time.time()
    
    print(f"⏱️ Submit button detection simulation: {end_detection - start_detection:.2f} seconds")
    print("-" * 80)
    
    # Close checker
    checker.close()
    
    total_time = time.time() - start_init
    print(f"🎯 TOTAL TEST TIME: {total_time:.2f} seconds")
    print("=" * 80)
    
    # Performance summary
    print("📊 PERFORMANCE SUMMARY:")
    print(f"   🚀 Initialization: {end_init - start_init:.2f}s")
    print(f"   ⚡ Parsing: {avg_parse_time:.3f}s per operation")
    print(f"   🔍 Detection: {end_detection - start_detection:.2f}s")
    print(f"   📈 Overall: {total_time:.2f}s total")
    print("=" * 80)
    
    # Performance rating
    if total_time < 10:
        print("🏆 PERFORMANCE RATING: EXCELLENT (< 10s)")
    elif total_time < 20:
        print("✅ PERFORMANCE RATING: GOOD (< 20s)")
    elif total_time < 30:
        print("⚠️ PERFORMANCE RATING: ACCEPTABLE (< 30s)")
    else:
        print("❌ PERFORMANCE RATING: NEEDS IMPROVEMENT (> 30s)")
    
    print("=" * 80)

if __name__ == "__main__":
    test_performance()
