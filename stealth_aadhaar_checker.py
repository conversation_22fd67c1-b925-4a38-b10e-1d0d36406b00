#!/usr/bin/env python3
"""
Stealth Aadhaar Validity Checker with Human Behavior Simulation

This enhanced version combines the original Aadhaar checker with advanced
stealth browser capabilities and human-like behavior simulation to minimize
captcha frequency and avoid detection.
"""

import time
import json
import logging
import random
from datetime import datetime
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from stealth_browser import StealthBrowser
from human_behavior import HumanBehaviorSimulator
from advanced_captcha_solver import NonOCRCaptchaSolver
from captcha_api_config import get_api_keys
from config import *


class StealthAadhaarChecker:
    """
    Enhanced Aadhaar validity checker with stealth capabilities and human behavior simulation.
    """
    
    def __init__(self, headless=False, debug=False, personality="normal"):
        """
        Initialize the stealth Aadhaar checker.
        
        Args:
            headless (bool): Run browser in headless mode
            debug (bool): Enable debug logging
            personality (str): Human behavior personality ('fast', 'normal', 'slow', 'elderly')
        """
        self.headless = headless
        self.debug = debug
        self.personality = personality
        
        # Initialize components
        self.stealth_browser = None
        self.human_behavior = None
        self.captcha_solver = None
        self.wait = None
        
        # Statistics
        self.stats = {
            'captchas_encountered': 0,
            'captchas_solved': 0,
            'navigation_attempts': 0,
            'successful_validations': 0,
            'stealth_score': 0.0
        }
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO if not debug else logging.DEBUG,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize all stealth and behavior components."""
        try:
            if self.debug:
                print("🚀 Initializing Stealth Aadhaar Checker...")
            
            # Initialize stealth browser
            self.stealth_browser = StealthBrowser(
                headless=self.headless, 
                debug=self.debug
            )
            
            # Initialize human behavior simulator
            self.human_behavior = HumanBehaviorSimulator(
                self.stealth_browser.driver,
                personality=self.personality
            )
            
            # Initialize captcha solver with API keys
            api_keys = get_api_keys()
            self.captcha_solver = NonOCRCaptchaSolver(
                debug=self.debug,
                api_keys=api_keys
            )
            
            # Initialize WebDriverWait
            self.wait = WebDriverWait(self.stealth_browser.driver, 20)
            
            if self.debug:
                print("✅ All components initialized successfully")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize components: {str(e)}")
            raise
    
    def check_aadhaar_validity(self, aadhaar_number, max_retries=3):
        """
        Check Aadhaar validity with stealth and human behavior.
        
        Args:
            aadhaar_number (str): Aadhaar number to validate
            max_retries (int): Maximum retry attempts
            
        Returns:
            dict: Validation results with stealth statistics
        """
        try:
            self.logger.info(f"🕵️ Starting stealth Aadhaar validation for: {aadhaar_number}")
            
            # Validate format
            if not self._validate_aadhaar_format(aadhaar_number):
                return self._create_error_response(
                    "Invalid Aadhaar number format",
                    "ValidationError",
                    aadhaar_number
                )
            
            # Clean Aadhaar number
            clean_aadhaar = aadhaar_number.replace(' ', '').replace('-', '')
            
            # Attempt validation with stealth retries
            for attempt in range(max_retries):
                self.logger.info(f"🎯 Stealth attempt {attempt + 1} of {max_retries}")
                
                try:
                    # Pre-navigation human behavior
                    if attempt > 0:
                        self._simulate_session_break()
                    
                    # Navigate with stealth
                    if not self._stealth_navigate():
                        continue
                    
                    # Simulate human page interaction
                    self._simulate_page_exploration()
                    
                    # Extract and solve captcha
                    captcha_result = self._handle_captcha_stealthily()
                    if not captcha_result['success']:
                        if attempt < max_retries - 1:
                            self._simulate_frustration_behavior()
                            continue
                        else:
                            return self._create_error_response(
                                captcha_result['error'],
                                "CaptchaError",
                                clean_aadhaar
                            )
                    
                    # Fill form with human behavior
                    if not self._fill_form_naturally(clean_aadhaar, captcha_result['text']):
                        continue
                    
                    # Wait for and extract results
                    result_data = self._extract_results_patiently()
                    
                    if result_data and result_data.get('success') is not False:
                        self.stats['successful_validations'] += 1
                        self._calculate_stealth_score()
                        
                        response = self._create_success_response(
                            result_data.get('validation_data', {}),
                            clean_aadhaar
                        )
                        response['stealth_stats'] = self._get_stealth_stats()
                        return response
                    
                except Exception as e:
                    self.logger.error(f"Attempt {attempt + 1} failed: {str(e)}")
                    if attempt < max_retries - 1:
                        self._simulate_error_recovery()
                        continue
            
            # All attempts failed
            return self._create_error_response(
                "All stealth attempts failed",
                "MaxRetriesExceeded",
                clean_aadhaar
            )
            
        except Exception as e:
            self.logger.error(f"Critical error in stealth validation: {str(e)}")
            return self._create_error_response(
                f"Critical stealth error: {str(e)}",
                "CriticalError",
                aadhaar_number
            )
    
    def _stealth_navigate(self):
        """Navigate to UIDAI website with stealth behavior."""
        try:
            self.stats['navigation_attempts'] += 1
            
            # Human-like navigation
            self.stealth_browser.human_navigate(UIDAI_URL)
            
            # Wait for page load with patience
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            # Simulate reading page content
            page_content = self.stealth_browser.driver.page_source
            self.human_behavior.simulate_reading_behavior(len(page_content))
            
            self.logger.info("✅ Stealth navigation successful")
            return True
            
        except Exception as e:
            self.logger.error(f"Stealth navigation failed: {str(e)}")
            return False
    
    def _simulate_page_exploration(self):
        """Simulate human exploration of the page."""
        try:
            # Random mouse movements
            self.stealth_browser._random_mouse_movements(2, 4)
            
            # Simulate looking around the page
            time.sleep(random.uniform(1, 3))
            
            # Occasional scroll to explore
            if random.random() < 0.7:
                scroll_amount = random.randint(100, 300)
                self.stealth_browser.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
                time.sleep(random.uniform(0.5, 1.5))
                self.stealth_browser.driver.execute_script("window.scrollTo(0, 0);")
            
            # Update fatigue and behavior
            self.human_behavior.update_fatigue()
            
        except Exception as e:
            self.logger.debug(f"Page exploration simulation failed: {str(e)}")
    
    def _handle_captcha_stealthily(self):
        """Handle captcha with stealth and human behavior."""
        try:
            self.stats['captchas_encountered'] += 1
            
            # Look for captcha with human-like scanning
            captcha_image = self._find_captcha_naturally()
            
            if not captcha_image:
                return {'success': False, 'error': 'No captcha found'}
            
            # Simulate studying the captcha
            time.sleep(random.uniform(1, 3))
            
            # Solve with non-OCR methods
            result = self.captcha_solver.solve_captcha_from_base64(
                captcha_image, 
                self.stealth_browser.driver
            )
            
            if result.get('success'):
                self.stats['captchas_solved'] += 1
                self.logger.info(f"🧩 Captcha solved using {result.get('method', 'unknown')}")
                return {'success': True, 'text': result.get('text', '')}
            else:
                self.logger.warning(f"❌ Captcha solving failed: {result.get('error', 'Unknown')}")
                return {'success': False, 'error': result.get('error', 'Captcha solving failed')}
                
        except Exception as e:
            self.logger.error(f"Stealth captcha handling failed: {str(e)}")
            return {'success': False, 'error': f'Captcha handling error: {str(e)}'}
    
    def _find_captcha_naturally(self):
        """Find captcha image with natural human scanning behavior."""
        # Simulate looking for captcha
        captcha_selectors = SELECTORS.get('captcha_images', [])

        for selector in captcha_selectors:
            try:
                # Simulate scanning the page
                time.sleep(random.uniform(0.5, 1.5))

                elements = self.stealth_browser.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed():
                        # Found captcha - simulate focusing on it
                        self.human_behavior.simulate_mouse_movement(element)
                        time.sleep(random.uniform(0.5, 1.0))

                        # Extract image
                        src = element.get_attribute('src')
                        if src:
                            # Handle different base64 formats
                            if 'data:image' in src or 'data:application/image' in src:
                                # Extract base64 part
                                if ',' in src:
                                    base64_data = src.split(',', 1)[1]
                                    if self.debug:
                                        print(f"🧩 Found captcha image: {src[:50]}...")
                                    return base64_data
                                else:
                                    if self.debug:
                                        print(f"🧩 Found captcha image (no comma): {src[:50]}...")
                                    return src

            except Exception as e:
                if self.debug:
                    print(f"⚠️ Error with selector {selector}: {str(e)}")
                continue

        # If no captcha found with selectors, try a more comprehensive search
        if self.debug:
            print("🔍 No captcha found with standard selectors, trying comprehensive search...")

        return self._comprehensive_captcha_search()

    def _comprehensive_captcha_search(self):
        """Comprehensive search for captcha images on the page."""
        try:
            # Search all images on the page
            all_images = self.stealth_browser.driver.find_elements(By.TAG_NAME, "img")

            for img in all_images:
                try:
                    if not img.is_displayed():
                        continue

                    src = img.get_attribute('src')
                    alt = img.get_attribute('alt') or ''
                    class_name = img.get_attribute('class') or ''

                    # Check if this looks like a captcha
                    captcha_indicators = ['captcha', 'verification', 'code', 'challenge']

                    if src and ('data:' in src and 'base64' in src):
                        # Check if it's likely a captcha based on attributes
                        is_likely_captcha = any(
                            indicator in (alt + class_name).lower()
                            for indicator in captcha_indicators
                        )

                        # Or if it's a base64 image in a form context
                        parent = img.find_element(By.XPATH, "..")
                        parent_class = parent.get_attribute('class') or ''
                        is_in_form_context = any(
                            term in parent_class.lower()
                            for term in ['form', 'auth', 'captcha', 'field']
                        )

                        if is_likely_captcha or is_in_form_context:
                            if self.debug:
                                print(f"🎯 Found potential captcha via comprehensive search")
                                print(f"   alt: {alt}")
                                print(f"   class: {class_name}")
                                print(f"   parent_class: {parent_class}")

                            # Extract base64 data
                            if ',' in src:
                                return src.split(',', 1)[1]
                            else:
                                return src

                except Exception as e:
                    continue

            if self.debug:
                print("❌ No captcha found in comprehensive search")
            return None

        except Exception as e:
            if self.debug:
                print(f"❌ Error in comprehensive captcha search: {str(e)}")
            return None
    
    def _fill_form_naturally(self, aadhaar_number, captcha_text):
        """Fill the form with natural human behavior."""
        try:
            # Find Aadhaar input field
            aadhaar_field = self._find_field_naturally(SELECTORS.get('aadhaar_input', []))
            if not aadhaar_field:
                return False
            
            # Type Aadhaar number with human behavior
            self.human_behavior.simulate_typing_pattern(aadhaar_field, aadhaar_number)
            
            # Brief pause between fields
            time.sleep(random.uniform(0.5, 2.0))
            
            # Find captcha input field
            captcha_field = self._find_field_naturally(SELECTORS.get('captcha_input', []))
            if not captcha_field:
                return False
            
            # Type captcha with human behavior
            self.human_behavior.simulate_typing_pattern(captcha_field, captcha_text)
            
            # Brief review pause
            time.sleep(random.uniform(1.0, 3.0))
            
            # Find and click submit button
            submit_button = self._find_field_naturally(SELECTORS.get('submit_button', []))
            if not submit_button:
                return False
            
            # Click with human behavior
            self.human_behavior.simulate_click_behavior(submit_button)
            
            self.logger.info("✅ Form filled with natural human behavior")
            return True
            
        except Exception as e:
            self.logger.error(f"Natural form filling failed: {str(e)}")
            return False
    
    def _find_field_naturally(self, selectors):
        """Find form field with natural human scanning."""
        for selector in selectors:
            try:
                # Simulate looking for the field
                time.sleep(random.uniform(0.2, 0.8))
                
                elements = self.stealth_browser.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        # Found field - simulate focusing
                        self.human_behavior.simulate_mouse_movement(element)
                        return element
                        
            except Exception:
                continue
        
        return None
    
    def _extract_results_patiently(self):
        """Extract results with patient human behavior."""
        try:
            # Wait patiently for results
            time.sleep(random.uniform(2, 5))
            
            # Simulate checking for results
            for _ in range(3):  # Multiple checks like a human would do
                try:
                    # Look for success indicators
                    success_elements = self.stealth_browser.driver.find_elements(
                        By.CSS_SELECTOR, 
                        ', '.join(SELECTORS.get('success_indicators', []))
                    )
                    
                    if success_elements:
                        for element in success_elements:
                            if element.is_displayed():
                                text = element.text.strip()
                                if any(keyword in text.lower() for keyword in SUCCESS_KEYWORDS):
                                    return {
                                        'success': True,
                                        'validation_data': {
                                            'status': 'Valid',
                                            'message': text,
                                            'source': 'success_indicator'
                                        }
                                    }
                    
                    # Look for error indicators
                    error_elements = self.stealth_browser.driver.find_elements(
                        By.CSS_SELECTOR,
                        ', '.join(SELECTORS.get('error_indicators', []))
                    )
                    
                    if error_elements:
                        for element in error_elements:
                            if element.is_displayed():
                                text = element.text.strip()
                                if any(keyword in text.lower() for keyword in ERROR_KEYWORDS):
                                    return {
                                        'success': True,
                                        'validation_data': {
                                            'status': 'Invalid',
                                            'message': text,
                                            'source': 'error_indicator'
                                        }
                                    }
                    
                    # Wait before next check
                    time.sleep(random.uniform(1, 2))
                    
                except Exception:
                    continue
            
            # Fallback to page content analysis
            page_text = self.stealth_browser.driver.find_element(By.TAG_NAME, "body").text.lower()
            
            if any(keyword in page_text for keyword in SUCCESS_KEYWORDS):
                return {
                    'success': True,
                    'validation_data': {
                        'status': 'Valid',
                        'message': 'Validation successful (detected in page content)',
                        'source': 'page_content'
                    }
                }
            elif any(keyword in page_text for keyword in ERROR_KEYWORDS):
                return {
                    'success': True,
                    'validation_data': {
                        'status': 'Invalid',
                        'message': 'Validation failed (detected in page content)',
                        'source': 'page_content'
                    }
                }
            
            return {'success': False, 'error': 'Could not extract results'}
            
        except Exception as e:
            self.logger.error(f"Result extraction failed: {str(e)}")
            return {'success': False, 'error': f'Result extraction error: {str(e)}'}
    
    def _simulate_session_break(self):
        """Simulate taking a break between attempts."""
        self.logger.info("😴 Taking a human-like break...")
        self.human_behavior.simulate_break_behavior()
    
    def _simulate_frustration_behavior(self):
        """Simulate human frustration after failed attempt."""
        # Longer pause (frustration)
        time.sleep(random.uniform(2, 5))
        
        # Random mouse movements (agitation)
        self.stealth_browser._random_mouse_movements(3, 6)
        
        # Possible page refresh
        if random.random() < 0.3:
            self.stealth_browser.driver.refresh()
            time.sleep(random.uniform(3, 6))
    
    def _simulate_error_recovery(self):
        """Simulate human error recovery behavior."""
        # Brief pause to process error
        time.sleep(random.uniform(1, 3))
        
        # Possible browser back/forward
        if random.random() < 0.2:
            self.stealth_browser.driver.back()
            time.sleep(random.uniform(1, 2))
            self.stealth_browser.driver.forward()
            time.sleep(random.uniform(1, 2))
    
    def _calculate_stealth_score(self):
        """Calculate stealth effectiveness score."""
        captcha_avoidance = 1.0 - (self.stats['captchas_encountered'] / max(1, self.stats['navigation_attempts']))
        captcha_success = self.stats['captchas_solved'] / max(1, self.stats['captchas_encountered'])
        validation_success = self.stats['successful_validations'] / max(1, self.stats['navigation_attempts'])
        
        self.stats['stealth_score'] = (captcha_avoidance * 0.4 + captcha_success * 0.3 + validation_success * 0.3) * 100
    
    def _get_stealth_stats(self):
        """Get stealth performance statistics."""
        behavior_stats = self.human_behavior.get_behavior_stats()
        
        return {
            'captchas_encountered': self.stats['captchas_encountered'],
            'captchas_solved': self.stats['captchas_solved'],
            'captcha_success_rate': f"{(self.stats['captchas_solved'] / max(1, self.stats['captchas_encountered'])) * 100:.1f}%",
            'navigation_attempts': self.stats['navigation_attempts'],
            'successful_validations': self.stats['successful_validations'],
            'stealth_score': f"{self.stats['stealth_score']:.1f}%",
            'human_behavior': behavior_stats
        }
    
    def _validate_aadhaar_format(self, aadhaar_number):
        """Validate Aadhaar number format."""
        clean_aadhaar = aadhaar_number.replace(' ', '').replace('-', '')
        return len(clean_aadhaar) == 12 and clean_aadhaar.isdigit()
    
    def _create_success_response(self, validation_data, aadhaar_number):
        """Create standardized success response."""
        return {
            "success": True,
            "aadhaar_number": aadhaar_number,
            "validation_data": validation_data,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "method": "stealth_automation"
        }
    
    def _create_error_response(self, error_message, error_type, aadhaar_number):
        """Create standardized error response."""
        return {
            "success": False,
            "error": error_message,
            "error_type": error_type,
            "aadhaar_number": aadhaar_number,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "method": "stealth_automation"
        }
    
    def close(self):
        """Close all components."""
        if self.stealth_browser:
            self.stealth_browser.close()
        
        if self.debug:
            print("🔒 Stealth Aadhaar Checker closed")
            
            # Print final stats
            stats = self._get_stealth_stats()
            print("\n📊 Final Stealth Statistics:")
            for key, value in stats.items():
                if key != 'human_behavior':
                    print(f"   {key}: {value}")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
