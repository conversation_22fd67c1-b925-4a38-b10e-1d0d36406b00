#!/usr/bin/env python3
"""
Stealth Aadhaar Validity Checker with Human Behavior Simulation

This enhanced version combines the original Aadhaar checker with advanced
stealth browser capabilities and human-like behavior simulation to minimize
captcha frequency and avoid detection.
"""

import time
import json
import logging
import random
from datetime import datetime
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from stealth_browser import StealthBrowser
from human_behavior import HumanBehaviorSimulator
from advanced_captcha_solver import NonOCRCaptchaSolver
from captcha_api_config import get_api_keys
from config import *


class StealthAadhaarChecker:
    """
    Enhanced Aadhaar validity checker with stealth capabilities and human behavior simulation.
    """
    
    def __init__(self, headless=False, debug=False, personality="normal"):
        """
        Initialize the stealth Aadhaar checker.
        
        Args:
            headless (bool): Run browser in headless mode
            debug (bool): Enable debug logging
            personality (str): Human behavior personality ('fast', 'normal', 'slow', 'elderly')
        """
        self.headless = headless
        self.debug = debug
        self.personality = personality
        
        # Initialize components
        self.stealth_browser = None
        self.human_behavior = None
        self.captcha_solver = None
        self.wait = None
        
        # Statistics
        self.stats = {
            'captchas_encountered': 0,
            'captchas_solved': 0,
            'navigation_attempts': 0,
            'successful_validations': 0,
            'stealth_score': 0.0
        }
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO if not debug else logging.DEBUG,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize all stealth and behavior components."""
        try:
            if self.debug:
                print("🚀 Initializing Stealth Aadhaar Checker...")
            
            # Initialize stealth browser
            self.stealth_browser = StealthBrowser(
                headless=self.headless, 
                debug=self.debug
            )
            
            # Initialize human behavior simulator
            self.human_behavior = HumanBehaviorSimulator(
                self.stealth_browser.driver,
                personality=self.personality
            )
            
            # Initialize captcha solver with API keys
            api_keys = get_api_keys()
            self.captcha_solver = NonOCRCaptchaSolver(
                debug=self.debug,
                api_keys=api_keys
            )
            
            # Initialize WebDriverWait
            self.wait = WebDriverWait(self.stealth_browser.driver, 20)
            
            if self.debug:
                print("✅ All components initialized successfully")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize components: {str(e)}")
            raise
    
    def check_aadhaar_validity(self, aadhaar_number, max_retries=3):
        """
        Check Aadhaar validity with stealth and human behavior.
        
        Args:
            aadhaar_number (str): Aadhaar number to validate
            max_retries (int): Maximum retry attempts
            
        Returns:
            dict: Validation results with stealth statistics
        """
        try:
            self.logger.info(f"🕵️ Starting stealth Aadhaar validation for: {aadhaar_number}")
            
            # Validate format
            if not self._validate_aadhaar_format(aadhaar_number):
                return self._create_error_response(
                    "Invalid Aadhaar number format",
                    "ValidationError",
                    aadhaar_number
                )
            
            # Clean Aadhaar number
            clean_aadhaar = aadhaar_number.replace(' ', '').replace('-', '')
            
            # Attempt validation with stealth retries
            for attempt in range(max_retries):
                self.logger.info(f"🎯 Stealth attempt {attempt + 1} of {max_retries}")
                
                try:
                    # Pre-navigation human behavior
                    if attempt > 0:
                        self._simulate_session_break()
                    
                    # Navigate with stealth
                    if not self._stealth_navigate():
                        continue
                    
                    # Simulate human page interaction
                    self._simulate_page_exploration()
                    
                    # Extract and solve captcha
                    captcha_result = self._handle_captcha_stealthily()
                    if not captcha_result['success']:
                        if attempt < max_retries - 1:
                            self._simulate_frustration_behavior()
                            continue
                        else:
                            return self._create_error_response(
                                captcha_result['error'],
                                "CaptchaError",
                                clean_aadhaar
                            )
                    
                    # Fill form with human behavior
                    if not self._fill_form_naturally(clean_aadhaar, captcha_result['text']):
                        continue
                    
                    # Wait for and extract results with timeout protection
                    print("🎯 Starting result extraction with timeout protection...")
                    try:
                        result_data = self._extract_results_patiently()
                    except Exception as e:
                        print(f"⚠️ Error during result extraction: {e}")
                        print("🔄 Trying quick result extraction as fallback...")
                        result_data = self._quick_result_extraction()

                    if result_data and result_data.get('success') is not False:
                        self.stats['successful_validations'] += 1
                        self._calculate_stealth_score()
                        
                        response = self._create_success_response(
                            result_data.get('validation_data', {}),
                            clean_aadhaar
                        )
                        response['stealth_stats'] = self._get_stealth_stats()
                        return response
                    
                except Exception as e:
                    self.logger.error(f"Attempt {attempt + 1} failed: {str(e)}")
                    if attempt < max_retries - 1:
                        self._simulate_error_recovery()
                        continue
            
            # All attempts failed
            return self._create_error_response(
                "All stealth attempts failed",
                "MaxRetriesExceeded",
                clean_aadhaar
            )
            
        except Exception as e:
            self.logger.error(f"Critical error in stealth validation: {str(e)}")
            return self._create_error_response(
                f"Critical stealth error: {str(e)}",
                "CriticalError",
                aadhaar_number
            )
    
    def _stealth_navigate(self):
        """Navigate to UIDAI website with stealth behavior."""
        try:
            self.stats['navigation_attempts'] += 1
            
            # Human-like navigation
            self.stealth_browser.human_navigate(UIDAI_URL)
            
            # Wait for page load with patience
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            # Simulate reading page content
            page_content = self.stealth_browser.driver.page_source
            self.human_behavior.simulate_reading_behavior(len(page_content))
            
            self.logger.info("✅ Stealth navigation successful")
            return True
            
        except Exception as e:
            self.logger.error(f"Stealth navigation failed: {str(e)}")
            return False
    
    def _simulate_page_exploration(self):
        """Simulate human exploration of the page."""
        try:
            # Random mouse movements
            self.stealth_browser._random_mouse_movements(2, 4)
            
            # Simulate looking around the page
            time.sleep(random.uniform(1, 3))
            
            # Occasional scroll to explore
            if random.random() < 0.7:
                scroll_amount = random.randint(100, 300)
                self.stealth_browser.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
                time.sleep(random.uniform(0.5, 1.5))
                self.stealth_browser.driver.execute_script("window.scrollTo(0, 0);")
            
            # Update fatigue and behavior
            self.human_behavior.update_fatigue()
            
        except Exception as e:
            self.logger.debug(f"Page exploration simulation failed: {str(e)}")
    
    def _handle_captcha_stealthily(self):
        """Handle captcha with stealth and human behavior."""
        try:
            self.stats['captchas_encountered'] += 1
            
            # Look for captcha with human-like scanning
            captcha_image = self._find_captcha_naturally()
            
            if not captcha_image:
                return {'success': False, 'error': 'No captcha found'}
            
            # Simulate studying the captcha
            time.sleep(random.uniform(1, 3))
            
            # Solve with non-OCR methods
            result = self.captcha_solver.solve_captcha_from_base64(
                captcha_image, 
                self.stealth_browser.driver
            )
            
            if result.get('success'):
                self.stats['captchas_solved'] += 1
                self.logger.info(f"🧩 Captcha solved using {result.get('method', 'unknown')}")
                return {'success': True, 'text': result.get('text', '')}
            else:
                self.logger.warning(f"❌ Captcha solving failed: {result.get('error', 'Unknown')}")
                return {'success': False, 'error': result.get('error', 'Captcha solving failed')}
                
        except Exception as e:
            self.logger.error(f"Stealth captcha handling failed: {str(e)}")
            return {'success': False, 'error': f'Captcha handling error: {str(e)}'}
    
    def _find_captcha_naturally(self):
        """Find captcha image with natural human scanning behavior."""
        # Simulate looking for captcha
        captcha_selectors = SELECTORS.get('captcha_images', [])

        for selector in captcha_selectors:
            try:
                # Simulate scanning the page
                time.sleep(random.uniform(0.5, 1.5))

                elements = self.stealth_browser.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed():
                        # Found captcha - simulate focusing on it
                        self.human_behavior.simulate_mouse_movement(element)
                        time.sleep(random.uniform(0.5, 1.0))

                        # Extract image
                        src = element.get_attribute('src')
                        if src:
                            # Handle different base64 formats
                            if 'data:image' in src or 'data:application/image' in src:
                                # Extract base64 part
                                if ',' in src:
                                    base64_data = src.split(',', 1)[1]
                                    if self.debug:
                                        print(f"🧩 Found captcha image: {src[:50]}...")
                                    return base64_data
                                else:
                                    if self.debug:
                                        print(f"🧩 Found captcha image (no comma): {src[:50]}...")
                                    return src

            except Exception as e:
                if self.debug:
                    print(f"⚠️ Error with selector {selector}: {str(e)}")
                continue

        # If no captcha found with selectors, try a more comprehensive search
        if self.debug:
            print("🔍 No captcha found with standard selectors, trying comprehensive search...")

        return self._comprehensive_captcha_search()

    def _comprehensive_captcha_search(self):
        """Comprehensive search for captcha images on the page."""
        try:
            # Search all images on the page
            all_images = self.stealth_browser.driver.find_elements(By.TAG_NAME, "img")

            for img in all_images:
                try:
                    if not img.is_displayed():
                        continue

                    src = img.get_attribute('src')
                    alt = img.get_attribute('alt') or ''
                    class_name = img.get_attribute('class') or ''

                    # Check if this looks like a captcha
                    captcha_indicators = ['captcha', 'verification', 'code', 'challenge']

                    if src and ('data:' in src and 'base64' in src):
                        # Check if it's likely a captcha based on attributes
                        is_likely_captcha = any(
                            indicator in (alt + class_name).lower()
                            for indicator in captcha_indicators
                        )

                        # Or if it's a base64 image in a form context
                        parent = img.find_element(By.XPATH, "..")
                        parent_class = parent.get_attribute('class') or ''
                        is_in_form_context = any(
                            term in parent_class.lower()
                            for term in ['form', 'auth', 'captcha', 'field']
                        )

                        if is_likely_captcha or is_in_form_context:
                            if self.debug:
                                print(f"🎯 Found potential captcha via comprehensive search")
                                print(f"   alt: {alt}")
                                print(f"   class: {class_name}")
                                print(f"   parent_class: {parent_class}")

                            # Extract base64 data
                            if ',' in src:
                                return src.split(',', 1)[1]
                            else:
                                return src

                except Exception as e:
                    continue

            if self.debug:
                print("❌ No captcha found in comprehensive search")
            return None

        except Exception as e:
            if self.debug:
                print(f"❌ Error in comprehensive captcha search: {str(e)}")
            return None

    def _find_submit_button_naturally(self):
        """Find submit button with comprehensive search including text-based detection."""
        print("🔍 Starting enhanced submit button detection...")

        # First try standard CSS selectors
        css_selectors = [
            "button[type='submit']",
            "input[type='submit']",
            "button.btn-primary",
            "button.submit",
            ".submit-btn",
            "#submit",
            "button[class*='proceed']",
            ".proceed-btn",
            "#proceed"
        ]

        for selector in css_selectors:
            try:
                print(f"🔍 Trying CSS selector: {selector}")
                time.sleep(random.uniform(0.3, 0.8))
                # Add timeout to prevent hanging
                elements = WebDriverWait(self.stealth_browser.driver, 5).until(
                    lambda driver: driver.find_elements(By.CSS_SELECTOR, selector)
                )
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        print(f"✅ Found submit button with selector: {selector}")
                        return element
            except Exception as e:
                print(f"⚠️ Error with selector {selector}: {str(e)}")
                continue

        print("🔍 CSS selectors failed, trying text-based search...")
        # Then try text-based search for buttons
        text_patterns = [
            'proceed', 'Proceed', 'PROCEED',
            'submit', 'Submit', 'SUBMIT',
            'verify', 'Verify', 'VERIFY',
            'check', 'Check', 'CHECK',
            'validate', 'Validate', 'VALIDATE',
            'continue', 'Continue', 'CONTINUE'
        ]

        try:
            # Search all buttons
            print("🔍 Searching all buttons for text patterns...")
            all_buttons = self.stealth_browser.driver.find_elements(By.TAG_NAME, "button")
            print(f"🔍 Found {len(all_buttons)} buttons to check")

            for button in all_buttons:
                if not button.is_displayed() or not button.is_enabled():
                    continue

                button_text = button.text.strip()
                button_value = button.get_attribute('value') or ''
                button_title = button.get_attribute('title') or ''
                button_aria_label = button.get_attribute('aria-label') or ''

                # Check if any text pattern matches
                all_text = f"{button_text} {button_value} {button_title} {button_aria_label}".lower()
                print(f"🔍 Checking button text: '{button_text}' (all_text: '{all_text}')")

                for pattern in text_patterns:
                    if pattern.lower() in all_text:
                        print(f"✅ Found submit button by text: '{button_text}' (pattern: {pattern})")
                        return button

            # Search all input buttons
            print("🔍 Searching all input buttons for text patterns...")
            all_inputs = self.stealth_browser.driver.find_elements(By.CSS_SELECTOR, "input[type='button'], input[type='submit']")
            print(f"🔍 Found {len(all_inputs)} input buttons to check")

            for input_elem in all_inputs:
                if not input_elem.is_displayed() or not input_elem.is_enabled():
                    continue

                input_value = input_elem.get_attribute('value') or ''
                input_title = input_elem.get_attribute('title') or ''

                all_text = f"{input_value} {input_title}".lower()
                print(f"🔍 Checking input value: '{input_value}' (all_text: '{all_text}')")

                for pattern in text_patterns:
                    if pattern.lower() in all_text:
                        print(f"✅ Found submit input by text: '{input_value}' (pattern: {pattern})")
                        return input_elem

        except Exception as e:
            print(f"⚠️ Error in text-based button search: {str(e)}")

        print("❌ No submit button found with any method")
        return None

    def _fill_form_naturally(self, aadhaar_number, captcha_text):
        """Fill the form with natural human behavior."""
        try:
            # Find Aadhaar input field
            aadhaar_field = self._find_field_naturally(SELECTORS.get('aadhaar_input', []))
            if not aadhaar_field:
                return False
            
            # Type Aadhaar number with human behavior
            self.human_behavior.simulate_typing_pattern(aadhaar_field, aadhaar_number)
            
            # Brief pause between fields
            time.sleep(random.uniform(0.5, 2.0))
            
            # Find captcha input field
            captcha_field = self._find_field_naturally(SELECTORS.get('captcha_input', []))
            if not captcha_field:
                return False
            
            # Type captcha with human behavior
            self.human_behavior.simulate_typing_pattern(captcha_field, captcha_text)
            
            # Brief review pause
            time.sleep(random.uniform(1.0, 3.0))
            
            # Find and click submit button
            submit_button = self._find_submit_button_naturally()
            if not submit_button:
                return False
            
            # Click with human behavior
            self.human_behavior.simulate_click_behavior(submit_button)
            
            self.logger.info("✅ Form filled with natural human behavior")
            return True
            
        except Exception as e:
            self.logger.error(f"Natural form filling failed: {str(e)}")
            return False
    
    def _find_field_naturally(self, selectors):
        """Find form field with natural human scanning."""
        for selector in selectors:
            try:
                # Simulate looking for the field
                time.sleep(random.uniform(0.2, 0.8))
                
                elements = self.stealth_browser.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        # Found field - simulate focusing
                        self.human_behavior.simulate_mouse_movement(element)
                        return element
                        
            except Exception:
                continue
        
        return None
    
    def _extract_results_patiently(self):
        """Extract results with patient human behavior."""
        try:
            print("🎯 Starting result extraction...")
            # Wait patiently for results
            time.sleep(random.uniform(2, 5))

            # Simulate checking for results
            for attempt in range(5):  # More attempts for better success
                try:
                    print(f"🔍 Result extraction attempt {attempt + 1}/5...")

                    # Enhanced success element detection
                    success_selectors = [
                        '.success', '.result', '.validation-result', '.aadhaar-result',
                        '[class*="success"]', '[class*="result"]', '[class*="valid"]',
                        '.alert-success', '.message-success', '.status-success',
                        '#result', '#validation-result', '#aadhaar-status',
                        'div[class*="result"]', 'span[class*="result"]', 'p[class*="result"]'
                    ]

                    # Look for success indicators
                    for selector in success_selectors:
                        try:
                            elements = self.stealth_browser.driver.find_elements(By.CSS_SELECTOR, selector)
                            for element in elements:
                                if element.is_displayed():
                                    text = element.text.strip()
                                    print(f"🔍 Checking element with selector '{selector}': '{text[:100]}...'")
                                    if text and any(keyword in text.lower() for keyword in SUCCESS_KEYWORDS):
                                        # Parse detailed validation information
                                        parsed_data = self._parse_validation_details(text)
                                        print(f"✅ Found validation results: {text}")
                                        return {
                                            'success': True,
                                            'validation_data': parsed_data
                                        }
                        except Exception as e:
                            print(f"⚠️ Error with selector '{selector}': {e}")
                            continue

                    # Enhanced error element detection
                    error_selectors = [
                        '.error', '.alert-error', '.message-error', '.status-error',
                        '[class*="error"]', '[class*="invalid"]', '[class*="fail"]',
                        '.alert-danger', '.text-danger', '.text-error',
                        '#error', '#validation-error', '#aadhaar-error'
                    ]

                    # Look for error indicators
                    for selector in error_selectors:
                        try:
                            elements = self.stealth_browser.driver.find_elements(By.CSS_SELECTOR, selector)
                            for element in elements:
                                if element.is_displayed():
                                    text = element.text.strip()
                                    print(f"🔍 Checking error element with selector '{selector}': '{text[:100]}...'")
                                    if text and any(keyword in text.lower() for keyword in ERROR_KEYWORDS):
                                        parsed_data = self._parse_validation_details(text)
                                        print(f"❌ Found validation error: {text}")
                                        return {
                                            'success': True,
                                            'validation_data': parsed_data
                                        }
                        except Exception as e:
                            print(f"⚠️ Error with error selector '{selector}': {e}")
                            continue

                    # Wait before next check
                    time.sleep(random.uniform(1, 2))

                except Exception as e:
                    print(f"⚠️ Error in result extraction attempt {attempt + 1}: {e}")
                    continue

            print("🔍 Fallback: Analyzing entire page content...")
            # Enhanced fallback to page content analysis
            try:
                page_text = self.stealth_browser.driver.find_element(By.TAG_NAME, "body").text
                print(f"📄 Page content length: {len(page_text)} characters")

                # Log a sample of the page content for debugging
                sample_text = page_text[:500] + "..." if len(page_text) > 500 else page_text
                print(f"📄 Page content sample: {sample_text}")

                if any(keyword in page_text.lower() for keyword in SUCCESS_KEYWORDS):
                    parsed_data = self._parse_validation_details(page_text)
                    print(f"✅ Found validation results in page content")
                    return {
                        'success': True,
                        'validation_data': parsed_data
                    }
                elif any(keyword in page_text.lower() for keyword in ERROR_KEYWORDS):
                    parsed_data = self._parse_validation_details(page_text)
                    print(f"❌ Found validation error in page content")
                    return {
                        'success': True,
                        'validation_data': parsed_data
                    }
                else:
                    # Try to find any text that looks like validation results
                    print("🔍 Searching for any validation-like content...")
                    if any(term in page_text.lower() for term in ['aadhaar', 'verification', 'valid', 'exists', 'age', 'gender', 'state']):
                        parsed_data = self._parse_validation_details(page_text)
                        print(f"🎯 Found potential validation content")
                        return {
                            'success': True,
                            'validation_data': parsed_data
                        }
            except Exception as e:
                print(f"⚠️ Error analyzing page content: {e}")

            # Final debugging: Save page source and screenshot
            try:
                print("📸 Saving page source and screenshot for debugging...")

                # Save page source
                page_source = self.stealth_browser.driver.page_source
                with open('debug_page_source.html', 'w', encoding='utf-8') as f:
                    f.write(page_source)
                print("💾 Page source saved to debug_page_source.html")

                # Save screenshot
                self.stealth_browser.driver.save_screenshot('debug_screenshot.png')
                print("📸 Screenshot saved to debug_screenshot.png")

                # Print current URL
                current_url = self.stealth_browser.driver.current_url
                print(f"🌐 Current URL: {current_url}")

            except Exception as e:
                print(f"⚠️ Error saving debug files: {e}")

            print("❌ Could not extract any validation results")
            return {'success': False, 'error': 'Could not extract results'}

        except Exception as e:
            self.logger.error(f"Result extraction failed: {str(e)}")
            return {'success': False, 'error': f'Result extraction error: {str(e)}'}

    def _quick_result_extraction(self):
        """Quick fallback result extraction method."""
        try:
            print("⚡ Quick result extraction started...")

            # Get page text immediately
            page_text = self.stealth_browser.driver.find_element(By.TAG_NAME, "body").text
            print(f"📄 Page text length: {len(page_text)} characters")

            # Quick check for validation keywords
            if any(keyword in page_text.lower() for keyword in ['exists', 'valid', 'verification completed', 'verified']):
                parsed_data = self._parse_validation_details(page_text)
                print(f"✅ Quick extraction found validation results")
                return {
                    'success': True,
                    'validation_data': parsed_data
                }
            elif any(keyword in page_text.lower() for keyword in ['invalid', 'not found', 'failed', 'error']):
                parsed_data = self._parse_validation_details(page_text)
                print(f"❌ Quick extraction found validation error")
                return {
                    'success': True,
                    'validation_data': parsed_data
                }
            else:
                print("❌ Quick extraction could not find validation results")
                return {'success': False, 'error': 'Quick extraction failed'}

        except Exception as e:
            print(f"⚠️ Error in quick result extraction: {e}")
            return {'success': False, 'error': f'Quick extraction error: {str(e)}'}

    def _parse_validation_details(self, text):
        """Parse detailed validation information from result text."""
        try:
            print(f"🔍 Parsing validation details from text: {text[:200]}...")

            # Initialize result structure
            result = {
                'status': 'Unknown',
                'aadhaar_number': None,
                'age_band': None,
                'gender': None,
                'state': None,
                'mobile': None,
                'message': text,
                'source': 'parsed_details'
            }

            # Clean up text
            text_lower = text.lower()
            lines = text.split('\n')
            print(f"🔍 Text split into {len(lines)} lines")

            # Determine status
            if any(keyword in text_lower for keyword in ['exists', 'valid', 'verification completed', 'verified']):
                result['status'] = 'Valid'
                print(f"✅ Status determined as: Valid")
            elif any(keyword in text_lower for keyword in ['invalid', 'not found', 'failed', 'error']):
                result['status'] = 'Invalid'
                print(f"❌ Status determined as: Invalid")

            # Extract Aadhaar number
            import re
            aadhaar_match = re.search(r'(\d{12})', text)
            if aadhaar_match:
                result['aadhaar_number'] = aadhaar_match.group(1)
                print(f"🆔 Found Aadhaar number: {result['aadhaar_number']}")

            # Parse line by line for structured data
            for i, line in enumerate(lines):
                line = line.strip()
                line_lower = line.lower()

                if line:  # Only process non-empty lines
                    print(f"🔍 Processing line {i}: '{line}'")

                # Age Band
                if 'age band' in line_lower and i + 1 < len(lines):
                    result['age_band'] = lines[i + 1].strip()
                    print(f"👤 Found age band: {result['age_band']}")
                elif re.search(r'\d+-\d+\s*years?', line):
                    result['age_band'] = line
                    print(f"👤 Found age band (pattern): {result['age_band']}")

                # Gender
                if 'gender' in line_lower and i + 1 < len(lines):
                    result['gender'] = lines[i + 1].strip()
                    print(f"⚧️ Found gender: {result['gender']}")
                elif line_lower in ['male', 'female', 'other']:
                    result['gender'] = line
                    print(f"⚧️ Found gender (direct): {result['gender']}")

                # State
                if 'state' in line_lower and i + 1 < len(lines):
                    result['state'] = lines[i + 1].strip()
                    print(f"🏛️ Found state: {result['state']}")
                elif any(state in line_lower for state in ['maharashtra', 'delhi', 'karnataka', 'tamil nadu', 'gujarat', 'rajasthan', 'uttar pradesh', 'west bengal']):
                    result['state'] = line
                    print(f"🏛️ Found state (pattern): {result['state']}")

                # Mobile
                if 'mobile' in line_lower and i + 1 < len(lines):
                    result['mobile'] = lines[i + 1].strip()
                    print(f"📱 Found mobile: {result['mobile']}")
                elif re.search(r'\*+\d+', line):
                    result['mobile'] = line
                    print(f"📱 Found mobile (pattern): {result['mobile']}")

            # Alternative parsing for compact format (colon-separated)
            print("🔍 Trying alternative parsing patterns...")

            # Try to extract from patterns like "Age Band: 20-30 years"
            age_match = re.search(r'age\s+band[:\s]*(\d+-\d+\s*years?)', text_lower)
            if age_match:
                result['age_band'] = age_match.group(1)
                print(f"👤 Found age band (regex): {result['age_band']}")

            gender_match = re.search(r'gender[:\s]*(male|female|other)', text_lower)
            if gender_match:
                result['gender'] = gender_match.group(1).upper()
                print(f"⚧️ Found gender (regex): {result['gender']}")

            state_match = re.search(r'state[:\s]*([a-zA-Z\s]+?)(?:\n|mobile|$)', text_lower)
            if state_match:
                state_text = state_match.group(1).strip()
                # Clean up state name
                if len(state_text) < 50:  # Reasonable state name length
                    result['state'] = state_text.title()
                    print(f"🏛️ Found state (regex): {result['state']}")

            mobile_match = re.search(r'mobile[:\s]*(\*+\d+)', text_lower)
            if mobile_match:
                result['mobile'] = mobile_match.group(1)
                print(f"📱 Found mobile (regex): {result['mobile']}")

            return result

        except Exception as e:
            if self.debug:
                print(f"⚠️ Error parsing validation details: {str(e)}")
            return {
                'status': 'Valid' if any(keyword in text.lower() for keyword in SUCCESS_KEYWORDS) else 'Unknown',
                'message': text,
                'source': 'raw_text'
            }

    def _simulate_session_break(self):
        """Simulate taking a break between attempts."""
        self.logger.info("😴 Taking a human-like break...")
        self.human_behavior.simulate_break_behavior()
    
    def _simulate_frustration_behavior(self):
        """Simulate human frustration after failed attempt."""
        # Longer pause (frustration)
        time.sleep(random.uniform(2, 5))
        
        # Random mouse movements (agitation)
        self.stealth_browser._random_mouse_movements(3, 6)
        
        # Possible page refresh
        if random.random() < 0.3:
            self.stealth_browser.driver.refresh()
            time.sleep(random.uniform(3, 6))
    
    def _simulate_error_recovery(self):
        """Simulate human error recovery behavior."""
        # Brief pause to process error
        time.sleep(random.uniform(1, 3))
        
        # Possible browser back/forward
        if random.random() < 0.2:
            self.stealth_browser.driver.back()
            time.sleep(random.uniform(1, 2))
            self.stealth_browser.driver.forward()
            time.sleep(random.uniform(1, 2))
    
    def _calculate_stealth_score(self):
        """Calculate stealth effectiveness score."""
        captcha_avoidance = 1.0 - (self.stats['captchas_encountered'] / max(1, self.stats['navigation_attempts']))
        captcha_success = self.stats['captchas_solved'] / max(1, self.stats['captchas_encountered'])
        validation_success = self.stats['successful_validations'] / max(1, self.stats['navigation_attempts'])
        
        self.stats['stealth_score'] = (captcha_avoidance * 0.4 + captcha_success * 0.3 + validation_success * 0.3) * 100
    
    def _get_stealth_stats(self):
        """Get stealth performance statistics."""
        behavior_stats = self.human_behavior.get_behavior_stats()
        
        return {
            'captchas_encountered': self.stats['captchas_encountered'],
            'captchas_solved': self.stats['captchas_solved'],
            'captcha_success_rate': f"{(self.stats['captchas_solved'] / max(1, self.stats['captchas_encountered'])) * 100:.1f}%",
            'navigation_attempts': self.stats['navigation_attempts'],
            'successful_validations': self.stats['successful_validations'],
            'stealth_score': f"{self.stats['stealth_score']:.1f}%",
            'human_behavior': behavior_stats
        }
    
    def _validate_aadhaar_format(self, aadhaar_number):
        """Validate Aadhaar number format."""
        clean_aadhaar = aadhaar_number.replace(' ', '').replace('-', '')
        return len(clean_aadhaar) == 12 and clean_aadhaar.isdigit()
    
    def _create_success_response(self, validation_data, aadhaar_number):
        """Create standardized success response."""
        return {
            "success": True,
            "aadhaar_number": aadhaar_number,
            "validation_data": validation_data,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "method": "stealth_automation"
        }
    
    def _create_error_response(self, error_message, error_type, aadhaar_number):
        """Create standardized error response."""
        return {
            "success": False,
            "error": error_message,
            "error_type": error_type,
            "aadhaar_number": aadhaar_number,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "method": "stealth_automation"
        }
    
    def close(self):
        """Close all components."""
        if self.stealth_browser:
            self.stealth_browser.close()
        
        if self.debug:
            print("🔒 Stealth Aadhaar Checker closed")
            
            # Print final stats
            stats = self._get_stealth_stats()
            print("\n📊 Final Stealth Statistics:")
            for key, value in stats.items():
                if key != 'human_behavior':
                    print(f"   {key}: {value}")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
