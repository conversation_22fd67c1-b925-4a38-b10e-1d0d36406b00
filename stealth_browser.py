#!/usr/bin/env python3
"""
Fallback Stealth Browser (No undetected-chromedriver dependency)

This version uses standard Selenium with manual stealth techniques
for compatibility with Python 3.13 and systems where undetected-chromedriver fails.
"""

import time
import random
import json
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager


class FallbackStealthBrowser:
    """
    Fallback stealth browser using standard Selenium with manual stealth techniques.
    """
    
    def __init__(self, headless=False, debug=False):
        self.headless = headless
        self.debug = debug
        self.driver = None
        self.action_chains = None
        
        # Human behavior parameters (OPTIMIZED FOR SPEED!)
        self.typing_speed_range = (0.02, 0.08)  # Reduced from (0.05, 0.25)
        self.mouse_move_speed = 0.2              # Reduced from 0.5
        self.click_delay_range = (0.05, 0.15)   # Reduced from (0.1, 0.5)
        self.scroll_delay_range = (0.1, 0.3)    # Reduced from (0.5, 2.0)
        self.page_read_time_range = (0.3, 0.8)  # MAJOR REDUCTION from (2, 5)!
        
        # User agent rotation
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
        
        self.setup_driver()
    
    def setup_driver(self):
        """Set up Chrome driver with manual stealth options."""
        try:
            if self.debug:
                print("🔧 Setting up fallback stealth browser...")
            
            # Chrome options for stealth
            options = Options()
            
            # Basic stealth options
            options.add_argument("--no-first-run")
            options.add_argument("--no-service-autorun") 
            options.add_argument("--password-store=basic")
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-gpu")
            options.add_argument("--disable-extensions")
            options.add_argument("--disable-plugins-discovery")
            options.add_argument("--disable-web-security")
            options.add_argument("--allow-running-insecure-content")
            
            # Random user agent
            user_agent = random.choice(self.user_agents)
            options.add_argument(f"--user-agent={user_agent}")
            
            # Random window size
            window_sizes = ["1920,1080", "1366,768", "1536,864", "1440,900"]
            window_size = random.choice(window_sizes)
            options.add_argument(f"--window-size={window_size}")
            
            if self.headless:
                options.add_argument("--headless=new")
            
            # Experimental options
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # Disable automation indicators
            prefs = {
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0,
                "profile.managed_default_content_settings.images": 1,
            }
            options.add_experimental_option("prefs", prefs)
            
            # Create driver with ChromeDriverManager
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=options)
            
            # Apply manual stealth scripts
            self._apply_stealth_scripts()
            
            # Set timeouts
            self.driver.implicitly_wait(10)
            self.driver.set_page_load_timeout(30)
            
            # Initialize action chains
            self.action_chains = ActionChains(self.driver)
            
            if self.debug:
                print("✅ Fallback stealth browser initialized successfully")
                
        except Exception as e:
            if self.debug:
                print(f"❌ Failed to initialize fallback stealth browser: {str(e)}")
            raise
    
    def _apply_stealth_scripts(self):
        """Apply JavaScript stealth scripts manually."""
        stealth_scripts = [
            # Remove webdriver property
            """
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            """,
            
            # Override plugins
            """
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            """,
            
            # Override languages
            """
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
            """,
            
            # Override chrome runtime
            """
            if (window.chrome) {
                Object.defineProperty(window.chrome, 'runtime', {
                    get: () => undefined,
                });
            }
            """,
            
            # Override permissions
            """
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
            """
        ]
        
        for script in stealth_scripts:
            try:
                self.driver.execute_script(script)
            except:
                pass
    
    def human_navigate(self, url):
        """Navigate to URL with human-like behavior."""
        if self.debug:
            print(f"🌐 Navigating to: {url}")
        
        # Quick delay before navigation (reduced from 0.5-2.0s to 0.2-0.5s)
        time.sleep(random.uniform(0.2, 0.5))
        
        self.driver.get(url)
        
        # Apply stealth scripts after navigation
        self._apply_stealth_scripts()
        
        # Simulate reading the page
        self._simulate_page_reading()
        
        # Quick mouse movements (reduced from 3-6 to 1-2)
        self._random_mouse_movements(1, 2)
        
        if self.debug:
            print("✅ Navigation completed with human-like behavior")
    
    def human_type(self, element, text, clear_first=True):
        """Type text with human-like speed and patterns."""
        if self.debug:
            print(f"⌨️ Typing: {text}")
        
        # Move to element first
        self._move_to_element_naturally(element)
        
        # Click to focus
        self._human_click(element, move_first=False)
        
        if clear_first:
            element.clear()
            time.sleep(random.uniform(0.1, 0.2))  # Reduced from 0.2-0.5s
        
        # Type with human-like speed
        for char in text:
            element.send_keys(char)
            delay = random.uniform(*self.typing_speed_range)
            if char == ' ':
                delay *= random.uniform(1.5, 3.0)
            time.sleep(delay)
        
        time.sleep(random.uniform(0.1, 0.3))  # Reduced from 0.3-0.8s
        
        if self.debug:
            print("✅ Typing completed")
    
    def human_click(self, element):
        """Click element with human-like behavior."""
        if self.debug:
            print("🖱️ Performing human-like click")
        
        self._human_click(element, move_first=True)
        
        if self.debug:
            print("✅ Click completed")
    
    def _human_click(self, element, move_first=True):
        """Internal method for human-like clicking."""
        if move_first:
            self._move_to_element_naturally(element)
        
        time.sleep(random.uniform(0.1, 0.3))  # Reduced click delay
        element.click()
        time.sleep(random.uniform(0.1, 0.3))  # Reduced click delay
    
    def _move_to_element_naturally(self, element):
        """Move mouse to element with natural movement."""
        try:
            self.action_chains.move_to_element(element).perform()
            time.sleep(random.uniform(0.1, 0.3))
        except Exception:
            pass
    
    def _random_mouse_movements(self, min_moves=2, max_moves=5):
        """Perform random mouse movements."""
        num_moves = random.randint(min_moves, max_moves)
        
        for _ in range(num_moves):
            x_offset = random.randint(-200, 200)
            y_offset = random.randint(-100, 100)
            
            try:
                self.action_chains.move_by_offset(x_offset, y_offset).perform()
                time.sleep(random.uniform(0.02, 0.1))  # Reduced from (0.1, 0.5)
            except:
                pass
    
    def _simulate_page_reading(self):
        """Simulate human reading behavior."""
        read_time = random.uniform(*self.page_read_time_range)
        scroll_count = random.randint(1, 3)
        scroll_interval = read_time / scroll_count
        
        for _ in range(scroll_count):
            time.sleep(scroll_interval * random.uniform(0.7, 1.3))
            
            scroll_amount = random.randint(100, 300)
            if random.random() < 0.5:
                scroll_amount = -scroll_amount
            
            try:
                self.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
            except:
                pass
        
        try:
            self.driver.execute_script("window.scrollTo(0, 0);")
        except:
            pass
        
        time.sleep(random.uniform(0.2, 0.5))  # Reduced from 0.5-1.5s
    
    def close(self):
        """Close the browser."""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
            self.driver = None
        
        if self.debug:
            print("🔒 Fallback stealth browser closed")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


# Alias for compatibility
StealthBrowser = FallbackStealthBrowser
