#!/usr/bin/env python3
"""
Test the smart selector caching system for performance optimization
"""

import sys
import os
import time

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stealth_aadhaar_checker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_smart_selector_performance():
    """Test the performance improvement from smart selector caching"""
    
    print("🧠 SMART SELECTOR SYSTEM PERFORMANCE TEST")
    print("=" * 80)
    
    # Create checker instance
    start_init = time.time()
    checker = StealthAadhaarChecker(debug=True)
    end_init = time.time()
    
    print(f"⏱️ Initialization time: {end_init - start_init:.2f} seconds")
    print("-" * 80)
    
    # Test 1: Navigate to a test page to trigger selector scanning
    print("🧪 Test 1: Smart Selector Scanning")
    start_scan = time.time()
    
    try:
        # Navigate to Google as a test page
        checker.stealth_browser.driver.get("https://www.google.com")
        
        # Trigger smart selector scanning
        checker._scan_page_selectors()
        
        end_scan = time.time()
        scan_time = end_scan - start_scan
        
        print(f"⏱️ Smart selector scan time: {scan_time:.2f} seconds")
        print(f"📊 Cached selectors by category:")
        for category, selectors in checker.selector_cache.items():
            if selectors:
                print(f"   {category}: {len(selectors)} selectors")
        
    except Exception as e:
        print(f"❌ Selector scanning test failed: {e}")
    
    print("-" * 80)
    
    # Test 2: Test cache hit performance
    print("🧪 Test 2: Cache Hit Performance")
    
    # Simulate multiple field searches to test cache hits
    start_cache_test = time.time()
    
    for i in range(5):
        print(f"🔍 Cache test iteration {i+1}/5")
        # This should use cached selectors on subsequent calls
        checker._scan_page_selectors()
    
    end_cache_test = time.time()
    cache_test_time = end_cache_test - start_cache_test
    
    print(f"⏱️ Cache hit test time: {cache_test_time:.2f} seconds")
    print(f"🎯 Cache hits: {checker.stats['selector_cache_hits']}")
    print(f"⚡ Average cache lookup: {cache_test_time/5:.3f} seconds")
    
    print("-" * 80)
    
    # Test 3: Performance comparison simulation
    print("🧪 Test 3: Performance Comparison Simulation")
    
    # Simulate old method (testing all selectors)
    old_method_selectors = [
        "input[name*='aadhaar']", "input[id*='aadhaar']", "input[placeholder*='aadhaar']",
        "input[name*='uid']", "input[id*='uid']", "input[placeholder*='uid']",
        "input[maxlength='12']", "input[type='text']", "input[type='number']",
        "button[type='submit']", "input[type='submit']", "button.btn-primary",
        "button.submit", ".submit-btn", "#submit"
    ]
    
    start_old = time.time()
    for selector in old_method_selectors:
        try:
            elements = checker.stealth_browser.driver.find_elements(
                checker.stealth_browser.By.CSS_SELECTOR, selector
            )
            time.sleep(0.1)  # Simulate old timing
        except:
            pass
    end_old = time.time()
    old_method_time = end_old - start_old
    
    # Simulate new method (using cache)
    start_new = time.time()
    cached_selectors = []
    for category in checker.selector_cache.values():
        cached_selectors.extend(category)
    
    for selector in cached_selectors[:len(old_method_selectors)]:  # Same number of tests
        try:
            elements = checker.stealth_browser.driver.find_elements(
                checker.stealth_browser.By.CSS_SELECTOR, selector
            )
            time.sleep(0.05)  # Optimized timing
        except:
            pass
    end_new = time.time()
    new_method_time = end_new - start_new
    
    print(f"⏱️ Old method time: {old_method_time:.2f} seconds")
    print(f"⚡ New method time: {new_method_time:.2f} seconds")
    
    if old_method_time > 0:
        improvement = ((old_method_time - new_method_time) / old_method_time) * 100
        print(f"📈 Performance improvement: {improvement:.1f}%")
    
    print("-" * 80)
    
    # Close checker
    checker.close()
    
    # Final statistics
    total_time = time.time() - start_init
    print(f"📊 SMART SELECTOR SYSTEM STATISTICS:")
    print(f"   🚀 Total test time: {total_time:.2f}s")
    print(f"   🧠 Selector scan time: {checker.stats['selector_scan_time']:.2f}s")
    print(f"   🎯 Cache hits: {checker.stats['selector_cache_hits']}")
    print(f"   📈 Cached selectors: {sum(len(s) for s in checker.selector_cache.values())}")
    
    print("=" * 80)
    
    # Performance rating
    if total_time < 8:
        print("🏆 SMART SELECTOR PERFORMANCE: EXCELLENT")
    elif total_time < 15:
        print("✅ SMART SELECTOR PERFORMANCE: GOOD")
    else:
        print("⚠️ SMART SELECTOR PERFORMANCE: NEEDS IMPROVEMENT")
    
    print("=" * 80)

if __name__ == "__main__":
    test_smart_selector_performance()
