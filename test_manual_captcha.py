#!/usr/bin/env python3
"""
Test Manual Captcha Input Mode

This script demonstrates the manual captcha input functionality
where you can manually enter captcha text while maintaining stealth features.
"""

import sys
import os
from stealth_aadhaar_checker import Stealth<PERSON><PERSON><PERSON><PERSON><PERSON>he<PERSON>


def test_manual_captcha_mode():
    """Test the manual captcha input mode."""
    print("🧪 Testing Manual Captcha Input Mode")
    print("=" * 50)
    
    # Test Aadhaar number
    test_aadhaar = "************"
    
    print(f"📱 Testing with Aadhaar: {test_aadhaar}")
    print("🎭 Using 'normal' personality for human behavior")
    print("👁️  Using visible browser mode for demonstration")
    print()
    
    try:
        # Initialize the stealth checker
        print("🚀 Initializing Stealth Aadhaar Checker...")
        checker = StealthAadhaarChecker(
            debug=True,
            headless=False,  # Visible mode
            personality='normal'
        )
        
        print("✅ Stealth checker initialized successfully!")
        print()
        print("📋 Manual Captcha Mode Features:")
        print("   ✅ GUI-based captcha display with image scaling")
        print("   ✅ Console fallback if GUI fails")
        print("   ✅ Automatic image opening")
        print("   ✅ User-friendly interface")
        print("   ✅ Skip option available")
        print("   ✅ Maintains all stealth features")
        print()
        
        # Start validation
        print("🎯 Starting validation with manual captcha input...")
        print("💡 When captcha appears, you'll see a GUI window to enter the text")
        print()
        
        result = checker.check_aadhaar_validity(test_aadhaar)
        
        print("\n" + "=" * 50)
        print("📊 VALIDATION RESULTS")
        print("=" * 50)
        
        if result.get('success'):
            print("✅ STATUS: SUCCESS")
            print(f"📱 Aadhaar: {test_aadhaar}")
            print(f"✅ Valid: {result.get('valid', 'Unknown')}")
            print(f"📄 Details: {result.get('details', 'N/A')}")
        else:
            print("❌ STATUS: FAILED")
            print(f"📱 Aadhaar: {test_aadhaar}")
            print(f"❗ Error: {result.get('error', 'Unknown error')}")
            print(f"🏷️ Error Type: {result.get('error_type', 'Unknown')}")
        
        # Show statistics
        stats = checker.get_stats()
        print(f"\n📊 Session Statistics:")
        for key, value in stats.items():
            if isinstance(value, float):
                print(f"   {key}: {value:.1f}%")
            else:
                print(f"   {key}: {value}")
        
        print("\n💡 MANUAL CAPTCHA MODE BENEFITS:")
        print("   🎯 100% accuracy (you enter the correct text)")
        print("   🆓 Completely free (no API costs)")
        print("   🕵️ Maintains full stealth behavior")
        print("   🤖 Human-like interactions preserved")
        print("   ⚡ Fast response time")
        print("   🔧 Perfect for testing and development")
        
        return result.get('success', False)
        
    except KeyboardInterrupt:
        print("\n❌ Test cancelled by user")
        return False
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False


def show_manual_mode_guide():
    """Show a guide for using manual captcha mode."""
    print("\n" + "🔧 MANUAL CAPTCHA MODE GUIDE")
    print("=" * 50)
    print()
    print("📋 How Manual Mode Works:")
    print("1. 🌐 Browser navigates to UIDAI website with stealth")
    print("2. ⌨️  Aadhaar number is entered with human-like typing")
    print("3. 🧩 Captcha is detected and extracted")
    print("4. 🖼️  GUI window opens showing the captcha image")
    print("5. 👤 You manually enter the captcha text")
    print("6. ✅ System submits the form and gets results")
    print()
    print("🎯 GUI Features:")
    print("   • 📏 Automatic image scaling for better visibility")
    print("   • 🎨 Clean, user-friendly interface")
    print("   • ⌨️  Keyboard shortcuts (Enter to submit)")
    print("   • ⏭️  Skip option if captcha is unclear")
    print("   • 🖱️  Click-based interaction")
    print()
    print("🔄 Fallback Options:")
    print("   • 💻 Console input if GUI fails")
    print("   • 📂 Automatic image file opening")
    print("   • 🔧 Manual file viewing instructions")
    print()
    print("💡 Best Practices:")
    print("   • ✅ Enter text exactly as shown")
    print("   • 🔤 Pay attention to case sensitivity")
    print("   • 🔍 Use the scaled image for better clarity")
    print("   • ⏭️  Skip if captcha is too unclear to read")
    print("   • 🔄 System will retry with new captcha if needed")


def main():
    """Main function."""
    print("🧩 MANUAL CAPTCHA INPUT MODE TESTER")
    print("=" * 60)
    print()
    print("This tool demonstrates the manual captcha input feature")
    print("that allows you to manually enter captcha text while")
    print("maintaining all stealth and human behavior features.")
    print()
    
    # Show guide
    show_manual_mode_guide()
    
    print("\n" + "🚀 STARTING TEST")
    print("=" * 50)
    
    # Ask user if they want to proceed
    try:
        response = input("\n🎯 Start manual captcha test? (y/n): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ Test cancelled")
            return
    except KeyboardInterrupt:
        print("\n❌ Test cancelled")
        return
    
    # Run the test
    success = test_manual_captcha_mode()
    
    print("\n" + "🎉 TEST COMPLETE")
    print("=" * 50)
    
    if success:
        print("✅ Manual captcha mode test completed successfully!")
        print("🎯 You can now use manual mode for reliable captcha solving")
    else:
        print("⚠️  Test completed with issues")
        print("🔧 Check the output above for details")
    
    print("\n💡 To use manual mode in your scripts:")
    print("   • Manual input is now the first method tried")
    print("   • No additional configuration needed")
    print("   • Works with all existing stealth features")
    print("   • Perfect for development and testing")


if __name__ == "__main__":
    main()
