<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UIDAI Aadhaar Verification - Simulator</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ccc; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="number"] { width: 100%; padding: 8px; border: 1px solid #ddd; }
        .captcha-container { display: flex; align-items: center; gap: 10px; }
        .captcha-image { border: 1px solid #ccc; padding: 5px; background: #f9f9f9; }
        .btn-primary { background: #007bff; color: white; padding: 10px 20px; border: none; cursor: pointer; }
        .result-success { background: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; margin-top: 20px; }
        .result-error { background: #f8d7da; color: #721c24; padding: 15px; border: 1px solid #f5c6cb; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="form-container">
        <h2>Aadhaar Verification</h2>

        <form id="aadhaarForm">
            <div class="form-group">
                <label for="aadhaar">Aadhaar Number:</label>
                <input type="text" id="aadhaar" name="aadhaar" maxlength="12" placeholder="Enter 12-digit Aadhaar number">
            </div>

            <div class="form-group">
                <label>Captcha:</label>
                <div class="captcha-container">
                    <img id="captcha" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
                         alt="Captcha Image" class="captcha-image" width="120" height="40">
                    <input type="text" id="captcha-input" name="captcha" maxlength="6" placeholder="Enter captcha">
                </div>
            </div>

            <div class="form-group">
                <button type="submit" id="proceed" class="btn-primary">Proceed</button>
            </div>
        </form>

        <!-- Success Result (hidden by default) -->
        <div id="success-result" class="result-success" style="display: none;">
            <h3>Verification Successful</h3>
            <p><strong>Status:</strong> Valid</p>
            <p><strong>Aadhaar Number:</strong> ************</p>
            <p><strong>Age Band:</strong> 20-30 years</p>
            <p><strong>Gender:</strong> MALE</p>
            <p><strong>State:</strong> Maharashtra</p>
            <p><strong>Mobile:</strong> *******671</p>
        </div>

        <!-- Error Result (hidden by default) -->
        <div id="error-result" class="result-error" style="display: none;">
            <h3>Verification Failed</h3>
            <p><strong>Status:</strong> Invalid</p>
            <p><strong>Error:</strong> Aadhaar number does not exist</p>
        </div>
    </div>

    <script>
        document.getElementById('aadhaarForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Simulate form submission
            const aadhaar = document.getElementById('aadhaar').value;
            const captcha = document.getElementById('captcha-input').value;

            // Hide both results first
            document.getElementById('success-result').style.display = 'none';
            document.getElementById('error-result').style.display = 'none';

            // Simulate validation (show success for demo)
            setTimeout(() => {
                if (aadhaar.length === 12 && captcha.length > 0) {
                    document.getElementById('success-result').style.display = 'block';
                } else {
                    document.getElementById('error-result').style.display = 'block';
                }
            }, 1000);
        });
    </script>
</body>
</html>