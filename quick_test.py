#!/usr/bin/env python3
"""
Quick Test Script for Stealth Features

This script performs a quick test to verify all components are working.
"""

def test_imports():
    """Test if all modules can be imported."""
    print("🧪 Testing Module Imports...")
    
    try:
        from stealth_browser import StealthBrowser
        print("✅ StealthBrowser imported successfully")
    except Exception as e:
        print(f"❌ StealthBrowser import failed: {e}")
        return False
    
    try:
        from human_behavior import HumanBehaviorSimulator
        print("✅ HumanBehaviorSimulator imported successfully")
    except Exception as e:
        print(f"❌ HumanBehaviorSimulator import failed: {e}")
        return False
    
    try:
        from advanced_captcha_solver import NonOCRCaptchaSolver
        print("✅ NonOCRCaptchaSolver imported successfully")
    except Exception as e:
        print(f"❌ NonOCRCaptchaSolver import failed: {e}")
        return False
    
    try:
        from stealth_aadhaar_checker import StealthAadhaarChecker
        print("✅ StealthAadhaarChecker imported successfully")
    except Exception as e:
        print(f"❌ StealthAadhaarChecker import failed: {e}")
        return False
    
    try:
        from captcha_api_config import get_api_keys, validate_api_keys
        print("✅ Captcha API config imported successfully")
    except Exception as e:
        print(f"❌ Captcha API config import failed: {e}")
        return False
    
    return True


def test_basic_functionality():
    """Test basic functionality without opening browser."""
    print("\n🔧 Testing Basic Functionality...")
    
    try:
        from captcha_api_config import validate_api_keys
        is_valid, message = validate_api_keys()
        print(f"API Keys Status: {message}")
        
        from advanced_captcha_solver import NonOCRCaptchaSolver
        solver = NonOCRCaptchaSolver(debug=False, api_keys={})
        stats = solver.get_stats()
        print(f"✅ Captcha solver initialized: {len(stats)} methods available")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False


def test_browser_creation():
    """Test browser creation (without navigation)."""
    print("\n🌐 Testing Browser Creation...")
    
    try:
        from stealth_browser import StealthBrowser
        
        # Test browser creation
        browser = StealthBrowser(headless=True, debug=False)
        print("✅ Stealth browser created successfully")
        
        # Test basic browser functionality
        browser.driver.get("data:text/html,<html><body><h1>Test</h1></body></html>")
        title = browser.driver.title
        print(f"✅ Browser navigation test passed")
        
        browser.close()
        print("✅ Browser closed successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Browser creation test failed: {e}")
        return False


def main():
    """Run all quick tests."""
    print("🚀 Quick Test Suite for Stealth Features")
    print("=" * 50)
    
    tests = [
        ("Module Imports", test_imports),
        ("Basic Functionality", test_basic_functionality),
        ("Browser Creation", test_browser_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your stealth setup is working correctly.")
        print("\nNext steps:")
        print("1. Configure captcha API keys in captcha_api_config_local.py")
        print("2. Run: python run_stealth_aadhaar.py")
        print("3. Test with a real Aadhaar validation")
    elif passed >= total - 1:
        print("⚠️ Most tests passed. Minor issues detected.")
        print("Your setup should work for basic functionality.")
    else:
        print("❌ Multiple test failures detected.")
        print("Please check your Python environment and dependencies.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
